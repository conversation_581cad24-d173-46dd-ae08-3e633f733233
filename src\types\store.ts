/**
 * Store 相关类型定义
 */

import type {
  AppError,
  CacheItem,
  SearchHistoryItem,
  UserPreferences,
  ThemeType,
  NetworkStatus
} from './common'
import type { 
  WeiboPost, 
  WeiboUser, 
  WeiboSearchParams,
  WeiboFilterOptions 
} from './weibo'
import type {
  AuthUser,
  AuthStatus
} from './user'

// Auth Store 状态类型
export interface AuthState {
  // 认证状态
  isAuthenticated: boolean
  authStatus: AuthStatus
  
  // 用户信息
  userInfo: AuthUser | null
  authToken: string | null
  
  // 加载和错误状态
  loading: boolean
  error: AppError | null
  
  // 会话信息
  sessionExpiry: number | null
  lastActivity: number | null
}

// Auth Store Getters 类型
export interface AuthGetters {
  isLoggedIn: boolean
  userName: string
  userAvatar: string
  hasError: boolean
  tokenPreview: string
  isTokenExpired: boolean
  timeUntilExpiry: number
}

// Auth Store Actions 类型
export interface AuthActions {
  // 初始化认证
  initAuth(): Promise<void>
  
  // 设置认证信息
  setAuth(token: string, userInfo: AuthUser): boolean
  
  // 处理认证回调
  handleAuthCallback(): Promise<boolean>
  
  // 刷新认证状态
  refreshAuth(): Promise<boolean>
  
  // 开始认证流程
  startAuth(): void
  
  // 登出
  logout(): Promise<void>
  
  // 获取认证头部
  getAuthHeaders(): Record<string, string>
  
  // 更新用户信息
  updateUserInfo(userInfo: Partial<AuthUser>): void
  
  // 设置错误
  setError(error: AppError | null): void
  
  // 设置加载状态
  setLoading(loading: boolean): void
}

// App Store 状态类型
export interface AppState {
  // 应用状态
  loading: boolean
  error: AppError | null
  
  // 主题和界面
  theme: ThemeType
  
  // 网络状态
  isOnline: boolean
  networkStatus: NetworkStatus
  
  // 当前页面信息
  currentPage: string
  pageTitle: string
  
  // 搜索历史
  searchHistory: SearchHistoryItem[]
  
  // 用户偏好
  preferences: UserPreferences
  
  // 应用配置
  config: {
    version: string
    buildTime: string
    environment: string
  }
}

// App Store Getters 类型
export interface AppGetters {
  hasError: boolean
  isDarkTheme: boolean
  canAutoRefresh: boolean
  recentSearches: SearchHistoryItem[]
  isSlowNetwork: boolean
}

// App Store Actions 类型
export interface AppActions {
  // 初始化应用
  initApp(): void
  
  // 设置加载状态
  setLoading(loading: boolean): void
  
  // 设置错误
  setError(error: AppError | null): void
  
  // 主题管理
  setTheme(theme: ThemeType): void
  toggleTheme(): void
  
  // 网络状态管理
  setNetworkStatus(status: NetworkStatus): void
  updateOnlineStatus(isOnline: boolean): void
  
  // 页面管理
  setCurrentPage(page: string, title?: string): void
  
  // 搜索历史管理
  addSearchHistory(keyword: string): void
  removeSearchHistory(keyword: string): void
  clearSearchHistory(): void
  
  // 偏好设置管理
  updatePreferences(preferences: Partial<UserPreferences>): void
  resetPreferences(): void
}

// Weibo Store 状态类型
export interface WeiboState {
  // 微博数据
  tweets: WeiboPost[]
  
  // 加载状态
  loading: boolean
  loadingMore: boolean
  hasMore: boolean
  
  // 错误状态
  error: AppError | null
  
  // 搜索状态
  isSearching: boolean
  searchQuery: string
  searchResults: WeiboPost[]
  
  // 当前用户
  currentUserId: string | null
  userProfiles: Map<string, WeiboUser>
  
  // 缓存
  cache: Map<string, CacheItem<any>>
  cacheExpiry: number
  
  // 分页信息
  pagination: {
    page: number
    pageSize: number
    total: number
    since_id?: string
    max_id?: string
  }
  
  // 过滤选项
  filterOptions: WeiboFilterOptions
}

// Weibo Store Getters 类型
export interface WeiboGetters {
  tweetsCount: number
  isLoading: boolean
  isLoadingMore: boolean
  hasMoreTweets: boolean
  isSearching: boolean
  currentUser: WeiboUser | null
  filteredTweets: WeiboPost[]
  hasError: boolean
}

// Weibo Store Actions 类型
export interface WeiboActions {
  // 初始化
  init(): void
  
  // 加载微博
  loadTweets(params?: any): Promise<void>
  loadMoreTweets(): Promise<void>
  refreshTweets(): Promise<void>
  
  // 搜索微博
  searchTweets(query: string, params?: WeiboSearchParams): Promise<void>
  clearSearch(): void
  
  // 用户微博
  loadUserTweets(userId: string, params?: any): Promise<void>
  
  // 状态管理
  setLoading(loading: boolean): void
  setLoadingMore(loading: boolean): void
  setError(error: AppError | null): void
  
  // 数据管理
  addTweets(tweets: WeiboPost[]): void
  updateTweet(tweetId: string, updates: Partial<WeiboPost>): void
  removeTweet(tweetId: string): void
  
  // 用户资料管理
  setUserProfile(userId: string, profile: WeiboUser): void
  getUserProfile(userId: string): WeiboUser | null
  
  // 缓存管理
  setCachedData(key: string, data: any, expiry?: number): void
  getCachedData(key: string): any | null
  clearCache(): void
  cleanExpiredCache(): void
  
  // 过滤管理
  setFilterOptions(options: Partial<WeiboFilterOptions>): void
  resetFilters(): void
  
  // 重置状态
  resetState(): void
}

// Store 模块类型
export interface StoreModules {
  auth: {
    state: AuthState
    getters: AuthGetters
    actions: AuthActions
  }
  app: {
    state: AppState
    getters: AppGetters
    actions: AppActions
  }
  weibo: {
    state: WeiboState
    getters: WeiboGetters
    actions: WeiboActions
  }
}

// Store 插件类型
export interface StorePlugin {
  install(store: any): void
}

// Store 配置类型
export interface StoreConfig {
  strict?: boolean
  devtools?: boolean
  plugins?: StorePlugin[]
}

// Store 持久化配置类型
export interface PersistConfig {
  key: string
  storage: Storage
  paths?: string[]
  beforeRestore?: (context: any) => void
  afterRestore?: (context: any) => void
}
