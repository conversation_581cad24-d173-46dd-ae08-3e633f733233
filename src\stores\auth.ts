import { defineStore } from 'pinia'
import { computed } from 'vue'
import { authService } from '../services/authService.ts'
import { getApiUrl, isDevelopment } from '../config/api.js'
import type { AuthState, AuthUser, AppError } from '@/types'

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    isAuthenticated: false,
    authStatus: 'unauthenticated',
    userInfo: null,
    authToken: null,
    loading: false,
    error: null,
    sessionExpiry: null,
    lastActivity: null
  }),

  getters: {
    isLoggedIn: (state): boolean => !!state.authToken,
    userName: (state): string => state.userInfo?.screen_name || '',
    userAvatar: (state): string => state.userInfo?.profile_image_url || '',
    hasError: (state): boolean => !!state.error,
    tokenPreview: (state): string => state.authToken ? state.authToken.substring(0, 10) + '...' : '',
    isTokenExpired: (state): boolean => {
      if (!state.sessionExpiry) return false
      return Date.now() > state.sessionExpiry
    },
    timeUntilExpiry: (state): number => {
      if (!state.sessionExpiry) return 0
      return Math.max(0, state.sessionExpiry - Date.now())
    }
  },

  actions: {
    // 初始化认证状态
    async initAuth(): Promise<void> {
      this.loading = true
      this.error = null

      try {
        const savedToken = authService.getToken()
        const savedUserInfo = authService.getUserInfo()

        if (savedToken) {
          this.authToken = savedToken
          this.userInfo = savedUserInfo
          this.isAuthenticated = true
          this.authStatus = 'authenticated'
          this.lastActivity = Date.now()

          if (isDevelopment()) {
            console.log('🔐 Auth initialized:', {
              hasToken: !!savedToken,
              tokenPreview: this.tokenPreview,
              userInfo: savedUserInfo
            })
          }
        } else {
          // 确保状态清空
          this.clearAuth()

          if (isDevelopment()) {
            console.log('🔒 Auth not initialized - no saved token')
          }
        }
      } catch (error: any) {
        this.error = { code: 'AUTH_INIT_ERROR', message: error.message }
        console.error('Failed to initialize auth:', error)
      } finally {
        this.loading = false
      }
    },

    // 处理后端认证回调 - 从 URL 参数中获取令牌和用户信息
    async handleAuthCallback(): Promise<boolean> {
      this.loading = true
      this.error = null

      try {
        const urlParams = new URLSearchParams(window.location.search)
        const token = urlParams.get('token')
        const user = urlParams.get('user')
        const authSuccess = urlParams.get('auth_success')
        const authError = urlParams.get('auth_error')

        if (isDevelopment()) {
          console.log('🔍 Auth callback check:', {
            hasToken: !!token,
            hasUser: !!user,
            authSuccess,
            authError,
            currentUrl: window.location.href
          })
        }

        // 处理认证成功
        if (token) {
          let userInfoData: AuthUser | null = null
          if (user) {
            try {
              userInfoData = JSON.parse(decodeURIComponent(user)) as AuthUser
            } catch (e) {
              console.warn('Failed to parse user info from URL:', e)
            }
          }

          const success = authService.setAuthenticated(token, userInfoData || undefined)
          if (success) {
            this.authToken = token
            this.userInfo = userInfoData
            this.isAuthenticated = true
            this.authStatus = 'authenticated'
            this.lastActivity = Date.now()

            if (isDevelopment()) {
              console.log('✅ Auth callback success:', {
                token: this.tokenPreview,
                userInfo: userInfoData
              })
            }

            // 清理 URL 参数 - History 模式下使用 pushState 避免页面刷新
            this.cleanUrlParams()

            return true
          } else {
            this.error = { code: 'AUTH_SET_FAILED', message: '认证设置失败' }
            console.error('❌ Failed to set authentication')
            return false
          }
        }

        // 处理认证错误
        if (authError) {
          const errorMessage = `认证失败: ${decodeURIComponent(authError)}`
          this.error = { code: 'AUTH_ERROR', message: errorMessage }
          console.error('❌ Auth callback error:', authError)
          this.cleanUrlParams()
          return false
        }

        // 处理认证成功标识（无令牌的情况）
        if (authSuccess === 'true') {
          this.cleanUrlParams()
          return true
        }

        return false
      } catch (error: any) {
        this.error = { code: 'AUTH_CALLBACK_ERROR', message: error.message }
        console.error('Auth callback error:', error)
        return false
      } finally {
        this.loading = false
      }
    },

    // 开始认证流程 - 跳转到后端认证接口
    startAuth() {
      this.loading = true
      this.error = null

      try {
        // 在 History 模式下，使用专门的回调路由
        const callbackUrl = window.location.origin + '/auth/callback'
        const backUrl = encodeURIComponent(callbackUrl)
        const authUrl = getApiUrl(`/api/oauth2?backto=${backUrl}`)
        const jumpUrl = getApiUrl(`/api/oauth2?redirect=${encodeURIComponent(authUrl)}`)

        if (isDevelopment()) {
          console.log('🚀 Starting auth flow:', {
            callbackUrl,
            backUrl: callbackUrl,
            authUrl,
            jumpUrl
          })
        }

        window.location.href = jumpUrl
      } catch (error: any) {
        this.error = { code: 'AUTH_START_ERROR', message: error.message }
        this.loading = false
        console.error('Failed to start auth:', error)
      }
    },

    // 设置认证状态 - 手动设置令牌和用户信息
    setAuth(token: string, userInfoData: AuthUser | null): boolean {
      if (!token || !token.trim()) {
        this.error = { code: 'INVALID_TOKEN', message: 'Invalid auth token provided' }
        console.warn('Invalid auth token provided to setAuth')
        return false
      }

      this.loading = true
      this.error = null

      try {
        const success = authService.setAuthenticated(token, userInfoData || undefined)
        if (success) {
          this.authToken = token
          this.userInfo = userInfoData
          this.isAuthenticated = true
          this.authStatus = 'authenticated'
          this.lastActivity = Date.now()

          if (isDevelopment()) {
            console.log('✅ Auth set manually:', {
              token: this.tokenPreview,
              userInfo: userInfoData
            })
          }

          return true
        }
        return false
      } catch (error: any) {
        this.error = { code: 'AUTH_SET_ERROR', message: error.message }
        console.error('Failed to set auth:', error)
        return false
      } finally {
        this.loading = false
      }
    },

    // 登出
    async logout(): Promise<void> {
      this.loading = true
      this.error = null

      try {
        this.clearAuth()
        authService.logout()

        if (isDevelopment()) {
          console.log('👋 User logged out')
        }
      } catch (error: any) {
        this.error = { code: 'LOGOUT_ERROR', message: error.message }
        console.error('Failed to logout:', error)
      } finally {
        this.loading = false
      }
    },

    // 获取认证头部，用于 API 请求
    getAuthHeaders(): Record<string, string> {
      const headers = authService.getAuthHeader()
      return headers as Record<string, string>
    },

    // 强制刷新认证状态 - 从 localStorage 重新加载
    async refreshAuth(): Promise<boolean> {
      if (isDevelopment()) {
        console.log('🔄 Refreshing auth state from localStorage...')
      }

      await this.initAuth()

      if (isDevelopment()) {
        console.log('✅ Auth state refreshed:', {
          isAuthenticated: this.isAuthenticated,
          hasToken: !!this.authToken
        })
      }

      return this.isAuthenticated
    },

    // 更新用户信息
    updateUserInfo(userInfo: Partial<AuthUser>): void {
      if (this.userInfo) {
        this.userInfo = { ...this.userInfo, ...userInfo }
        authService.setUserInfo(this.userInfo)
      }
    },

    // 设置错误
    setError(error: AppError | null): void {
      this.error = error
    },

    // 设置加载状态
    setLoading(loading: boolean): void {
      this.loading = loading
    },

    // 清空认证状态
    clearAuth(): void {
      this.authToken = null
      this.userInfo = null
      this.isAuthenticated = false
      this.authStatus = 'unauthenticated'
      this.error = null
      this.sessionExpiry = null
      this.lastActivity = null
    },

    // 清理 URL 参数
    cleanUrlParams(): void {
      const cleanUrl = window.location.origin + window.location.pathname
      window.history.pushState({}, document.title, cleanUrl)
    }
  }
})

// 为了向后兼容，保留 useAuth 函数
export const useAuth = () => {
  const store = useAuthStore()

  return {
    // 状态 (保持 computed 包装以兼容现有代码)
    isAuthenticated: computed(() => store.isAuthenticated),
    userInfo: computed(() => store.userInfo),
    authToken: computed(() => store.authToken),
    loading: computed(() => store.loading),
    error: computed(() => store.error),

    // 方法
    initAuth: () => store.initAuth(),
    handleAuthCallback: () => store.handleAuthCallback(),
    setAuth: (token: string, userInfo: AuthUser | null) => store.setAuth(token, userInfo),
    startAuth: () => store.startAuth(),
    logout: () => store.logout(),
    getAuthHeaders: () => store.getAuthHeaders(),
    refreshAuth: () => store.refreshAuth(),
    updateUserInfo: (userInfo: Partial<AuthUser>) => store.updateUserInfo(userInfo),
    setError: (error: AppError | null) => store.setError(error),
    setLoading: (loading: boolean) => store.setLoading(loading)
  }
}