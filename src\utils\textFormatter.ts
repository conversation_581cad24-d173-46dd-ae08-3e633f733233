import DOMPurify from 'dompurify'
import { imageProxy } from './imageProxy.ts'

class TextFormatter {
  formatText(text: string | null | undefined): string {
    if (!text) return ''

    DOMPurify.addHook('beforeSanitizeElements' as any, (node: Element) => {
      if (node.nodeType === 1 && node.tagName === 'A') {
        const content = node.textContent?.trim() || ''
        if (content.startsWith('#') && content.endsWith('#')) {
          node.setAttribute('data-topic', 'true')
        }
        if (content.startsWith('@')) {
          node.setAttribute('data-at-user', 'true')
        }
      }
    })

    const cleanHTML = DOMPurify.sanitize(text.trim(), {
      ALLOWED_TAGS: ['a', 'img', 'br', 'span'],
      ALLOWED_ATTR: ['src', 'data-topic', 'data-at-user', 'style'],
    })

    DOMPurify.removeHook('beforeSanitizeElements')

    const div = document.createElement('div')
    div.innerHTML = cleanHTML

    div.querySelectorAll('a[data-topic]').forEach(a => {
      const span = document.createElement('span')
      span.className = 'topic-tag'
      span.innerHTML = a.innerHTML
      a.replaceWith(span)
    })

    div.querySelectorAll('a[data-at-user]').forEach(a => {
      const span = document.createElement('span')
      span.className = 'at-user-tag'
      span.innerHTML = a.innerHTML
      a.replaceWith(span)
    })

    div.querySelectorAll('a:not([data-topic]):not([data-at-user])').forEach(a => {
      a.replaceWith(...a.childNodes)
    })

    div.querySelectorAll('img').forEach(img => {
      img.className = 'inline-emoji'
      img.src = imageProxy.proxyImage(img.src)
    })

    return div.innerHTML
  }
}

export const textFormatter = new TextFormatter()
