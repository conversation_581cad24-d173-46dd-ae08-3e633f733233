<template>
  <div class="auth-callback">
    <el-loading-directive v-loading="loading" text="正在处理认证回调...">
      <div class="callback-content">
        <el-icon class="callback-icon" :size="48">
          <Loading />
        </el-icon>
        <p class="callback-text">正在验证认证信息...</p>
      </div>
    </el-loading-directive>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '../stores/auth.ts'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'

const router = useRouter()
const { handleAuthCallback } = useAuth()
const loading = ref(true)

onMounted(async () => {
  try {
    console.log('🔄 AuthCallback component mounted, processing...')
    
    // 延迟处理，确保页面完全加载
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const result = handleAuthCallback()
    console.log('🔍 Auth callback result:', result)
    
    if (result.success) {
      if (result.message) {
        ElMessage.success(result.message)
      }
      
      console.log('✅ Auth callback successful, redirecting to home...')
      
      // 认证成功，延迟跳转确保状态完全更新
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 使用 replace 而不是 push，避免在历史记录中保留回调页面
      router.replace('/')
    } else if (result.message) {
      ElMessage.error(result.message)
      console.log('❌ Auth callback failed, redirecting to home...')
      router.replace('/')
    } else {
      // 没有认证参数，直接跳转到首页
      console.log('ℹ️ No auth parameters found, redirecting to home...')
      router.replace('/')
    }
  } catch (error) {
    console.error('Auth callback error:', error)
    ElMessage.error('认证处理出错')
    router.replace('/')
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.auth-callback {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.callback-content {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.callback-icon {
  color: #409eff;
  margin-bottom: 1rem;
}

.callback-text {
  color: #606266;
  font-size: 16px;
  margin: 0;
}
</style>