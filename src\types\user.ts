/**
 * 用户相关类型定义
 */

import type { WeiboUser } from './weibo'

// 认证状态类型
export type AuthStatus = 'authenticated' | 'unauthenticated' | 'pending' | 'expired'

// 认证令牌类型
export interface AuthToken {
  access_token: string
  token_type: 'Bearer'
  expires_in?: number
  refresh_token?: string
  scope?: string
  created_at?: number
}

// 认证用户信息类型
export interface AuthUser extends WeiboUser {
  // 扩展认证用户特有的属性
  email?: string
  phone?: string
  birthday?: string
  lang?: string
  time_zone?: string
  
  // 权限相关
  permissions?: string[]
  roles?: string[]
  
  // 账户状态
  status?: 'active' | 'suspended' | 'pending'
  verified_email?: boolean
  verified_phone?: boolean
  
  // 最后活动时间
  last_login?: string
  last_activity?: string
  
  // 偏好设置
  preferences?: UserPreferences
}

// 用户偏好设置类型
export interface UserPreferences {
  // 界面设置
  theme: 'light' | 'dark' | 'auto'
  language: 'zh-CN' | 'en-US'
  timezone: string
  
  // 内容设置
  showSensitiveContent: boolean
  hideRetweets: boolean
  hideAds: boolean
  autoRefresh: boolean
  refreshInterval: number // 秒
  
  // 媒体设置
  imageQuality: 'low' | 'medium' | 'high'
  autoPlayVideo: boolean
  preloadImages: boolean
  
  // 通知设置
  enableNotifications: boolean
  notificationTypes: NotificationType[]
  
  // 隐私设置
  showOnlineStatus: boolean
  allowDirectMessages: boolean
  
  // 显示设置
  postsPerPage: number
  showTimestamps: boolean
  compactMode: boolean
}

// 通知类型
export type NotificationType = 
  | 'mention'
  | 'comment'
  | 'like'
  | 'repost'
  | 'follow'
  | 'message'
  | 'system'

// 用户资料更新类型
export interface UserProfileUpdate {
  screen_name?: string
  name?: string
  description?: string
  location?: string
  url?: string
  profile_image?: File | string
  preferences?: Partial<UserPreferences>
}

// 用户统计信息类型
export interface UserStats {
  followers_count: number
  friends_count: number
  statuses_count: number
  favourites_count: number
  listed_count?: number
  
  // 活动统计
  posts_today: number
  posts_week: number
  posts_month: number
  
  // 互动统计
  likes_received: number
  reposts_received: number
  comments_received: number
}

// 用户关系类型
export type UserRelationship = 
  | 'none'
  | 'following'
  | 'followed_by'
  | 'mutual'
  | 'blocked'
  | 'muted'

// 用户搜索参数类型
export interface UserSearchParams {
  q: string // 搜索关键词
  page?: number
  count?: number
  sort?: 'followers' | 'posts' | 'relevance'
  verified?: boolean
  location?: string
}

// 用户列表响应类型
export interface UserListResponse {
  users: WeiboUser[]
  total_number?: number
  next_cursor?: string
  previous_cursor?: string
}

// 关注/粉丝列表参数类型
export interface FollowListParams {
  uid: string | number
  screen_name?: string
  page?: number
  count?: number
  cursor?: string
}

// 用户活动记录类型
export interface UserActivity {
  id: string
  type: 'post' | 'like' | 'repost' | 'comment' | 'follow'
  target_id?: string
  target_type?: 'post' | 'user' | 'comment'
  created_at: string
  metadata?: Record<string, any>
}

// 用户会话信息类型
export interface UserSession {
  id: string
  user_id: string
  device_id?: string
  device_type?: 'web' | 'mobile' | 'desktop'
  ip_address?: string
  user_agent?: string
  location?: string
  created_at: string
  last_activity: string
  expires_at?: string
  is_current?: boolean
}

// 用户安全设置类型
export interface UserSecuritySettings {
  two_factor_enabled: boolean
  login_notifications: boolean
  suspicious_activity_alerts: boolean
  session_timeout: number // 分钟
  allowed_devices?: string[]
  blocked_ips?: string[]
}

// 用户导出数据类型
export interface UserDataExport {
  user_info: AuthUser
  posts: any[]
  likes: any[]
  follows: any[]
  followers: any[]
  settings: UserPreferences
  activities: UserActivity[]
  export_date: string
  format: 'json' | 'csv' | 'xml'
}

// 认证回调参数类型
export interface AuthCallbackParams {
  code?: string
  state?: string
  error?: string
  error_description?: string
  token?: string
  user?: string
  auth_success?: string
}

// 认证错误类型
export interface AuthError {
  code: string
  message: string
  description?: string
  uri?: string
}

// OAuth 配置类型
export interface OAuthConfig {
  client_id: string
  client_secret?: string
  redirect_uri: string
  scope: string[]
  response_type: 'code' | 'token'
  state?: string
}

// 登录表单类型
export interface LoginForm {
  username: string
  password: string
  remember_me?: boolean
  captcha?: string
}

// 注册表单类型
export interface RegisterForm {
  username: string
  email: string
  password: string
  confirm_password: string
  phone?: string
  captcha: string
  agree_terms: boolean
}
