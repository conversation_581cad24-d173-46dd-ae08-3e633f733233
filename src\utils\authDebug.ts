/**
 * 认证流程调试工具
 */
import { useAuth } from '../stores/auth.ts'
import { authService } from '../services/authService.ts'

interface AuthDebugResult {
  isAuthenticated: boolean
  hasToken: boolean
  consistent: boolean
}

// 声明全局类型
declare global {
  interface Window {
    debugAuth?: () => AuthDebugResult
  }
}

export const debugAuth = (): AuthDebugResult => {
  const { isAuthenticated, authToken, userInfo } = useAuth()
  
  console.group('🔍 Auth Debug Info')
  
  // 检查 store 状态
  console.log('Store State:', {
    isAuthenticated: isAuthenticated.value,
    hasAuthToken: !!authToken.value,
    authTokenPreview: authToken.value ? authToken.value.substring(0, 10) + '...' : null,
    userInfo: userInfo.value
  })
  
  // 检查 localStorage
  const localToken = authService.getToken()
  const localUserInfo = authService.getUserInfo()
  
  console.log('LocalStorage State:', {
    hasToken: !!localToken,
    tokenPreview: localToken ? localToken.substring(0, 10) + '...' : null,
    userInfo: localUserInfo
  })
  
  // 检查认证头部
  const authHeaders = authService.getAuthHeader()
  console.log('Auth Headers:', authHeaders)
  
  // 状态一致性检查
  const storeTokenMatch = authToken.value === localToken
  const storeAuthMatch = isAuthenticated.value === !!localToken
  
  console.log('Consistency Check:', {
    storeTokenMatchesLocal: storeTokenMatch,
    storeAuthMatchesLocal: storeAuthMatch,
    allConsistent: storeTokenMatch && storeAuthMatch
  })
  
  if (!storeTokenMatch || !storeAuthMatch) {
    console.warn('⚠️ State inconsistency detected!')
  }
  
  console.groupEnd()
  
  return {
    isAuthenticated: isAuthenticated.value,
    hasToken: !!authToken.value,
    consistent: storeTokenMatch && storeAuthMatch
  }
}

// 在开发环境中提供全局访问
if (import.meta.env?.DEV && typeof window !== 'undefined') {
  window.debugAuth = debugAuth
  console.log('🎯 Use window.debugAuth() to check auth state')
}
