import type { AuthUser } from '../types'

interface AuthHeaders {
  Authorization?: string
}

class AuthService {
  // 令牌管理 - 只处理后端返回的认证令牌
  setToken(token: string | null): void {
    try {
      if (token) {
        localStorage.setItem('auth_token', token)
      }
    } catch (error) {
      console.error('Failed to save auth token:', error)
    }
  }

  getToken(): string | null {
    try {
      return localStorage.getItem('auth_token')
    } catch (error) {
      console.error('Failed to get auth token:', error)
      return null
    }
  }

  setUserInfo(userInfo: AuthUser | null): void {
    try {
      if (userInfo) {
        localStorage.setItem('user_info', JSON.stringify(userInfo))
      }
    } catch (error) {
      console.error('Failed to save user info:', error)
    }
  }

  getUserInfo(): AuthUser | null {
    try {
      const userInfo = localStorage.getItem('user_info')
      return userInfo ? JSON.parse(userInfo) as AuthUser : null
    } catch (error) {
      console.error('Failed to get user info:', error)
      return null
    }
  }

  clearAuth(): void {
    try {
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_info')
    } catch (error) {
      console.error('Failed to clear auth data:', error)
    }
  }

  // 检查是否已认证 - 检查是否有有效的令牌
  isAuthenticated(): boolean {
    const token = this.getToken()
    return !!(token && token.trim())
  }

  // 设置认证状态 - 从后端回调中获取令牌和用户信息
  setAuthenticated(token: string, userInfo?: AuthUser): boolean {
    if (!token || !token.trim()) {
      console.warn('Invalid auth token provided')
      return false
    }
    
    this.setToken(token)
    if (userInfo) {
      this.setUserInfo(userInfo)
    }
    return true
  }

  // 登出 - 清理本地状态
  logout(): void {
    this.clearAuth()
  }

  // 获取认证头部，用于 API 请求
  getAuthHeader(): AuthHeaders {
    const token = this.getToken()
    return token ? { 'Authorization': `Bearer ${token}` } : {}
  }
}

export const authService = new AuthService()
