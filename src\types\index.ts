/**
 * 类型定义统一导出
 */

// 通用类型
export type {
  ApiResponse,
  PaginationParams,
  PaginationInfo,
  LoadingState,
  AppError,
  ThemeType,
  NetworkStatus,
  CacheItem,
  SearchHistoryItem,
  UserPreferences,
  AppConfig,
  AppEvent,
  RouteMeta,
  BaseComponentProps,
  ValidationRule,
  FileUploadOptions,
  ImageProcessOptions,
  Optional,
  RequiredFields,
  DeepPartial,
  AsyncFunction,
  EventHandler,
  Callback
} from './common'

// 微博相关类型
export type {
  WeiboUser,
  WeiboImage,
  WeiboVideo,
  WeiboTopic,
  WeiboGeo,
  WeiboRetweetInfo,
  WeiboPost,
  WeiboListResponse,
  WeiboSearchParams,
  WeiboDetailParams,
  UserWeiboParams,
  WeiboStats,
  WeiboCacheKey,
  WeiboAction,
  WeiboFilterOptions
} from './weibo'

// 用户相关类型
export type {
  AuthStatus,
  AuthToken,
  AuthUser,
  NotificationType,
  UserProfileUpdate,
  UserStats,
  UserRelationship,
  UserSearchParams,
  UserListResponse,
  FollowListParams,
  UserActivity,
  UserSession,
  UserSecuritySettings,
  UserDataExport,
  AuthCallbackParams,
  AuthError,
  OAuthConfig,
  LoginForm,
  RegisterForm
} from './user'

// API 相关类型
export type {
  HttpMethod,
  RequestConfig,
  ApiResponseData,
  ApiError,
  ApiEndpoint,
  ApiConfig,
  WeiboApiService,
  UserApiService,
  AuthApiService,
  UploadApiService,
  CacheApiService,
  RequestInterceptor,
  ResponseInterceptor,
  ErrorInterceptor,
  ApiClient,
  ApiState,
  ApiStats,
  BatchRequestConfig,
  BatchRequestResult
} from './api'

// Store 相关类型
export type {
  AuthState,
  AuthGetters,
  AuthActions,
  AppState,
  AppGetters,
  AppActions,
  WeiboState,
  WeiboGetters,
  WeiboActions,
  StoreModules,
  StorePlugin,
  StoreConfig,
  PersistConfig
} from './store'

// 组件 Props 类型 (可以根据需要扩展)
import type { WeiboPost, WeiboImage, WeiboUser } from './weibo'

export interface WeiboCardProps {
  weibo: WeiboPost
  showActions?: boolean
  compact?: boolean
}

export interface ImageGridProps {
  images: WeiboImage[]
  maxDisplay?: number
  aspectRatio?: number
}

export interface UserHeaderProps {
  user: WeiboUser
  showStats?: boolean
  showFollowButton?: boolean
}

export interface SearchBoxProps {
  placeholder?: string
  suggestions?: string[]
  loading?: boolean
  onSearch?: (query: string) => void
  onSuggestionSelect?: (suggestion: string) => void
}

export interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large'
  text?: string
  overlay?: boolean
}

export interface ErrorBoundaryProps {
  fallback?: any
  onError?: (error: Error, errorInfo: any) => void
}

// 路由相关类型
export interface RouteParams {
  userId?: string
  weiboId?: string
  [key: string]: string | undefined
}

export interface RouteQuery {
  page?: string
  q?: string
  type?: string
  [key: string]: string | undefined
}

// 事件类型
export interface WeiboCardEvent {
  type: 'like' | 'repost' | 'comment' | 'share'
  weiboId: string
  data?: any
}

export interface SearchEvent {
  type: 'search' | 'suggestion' | 'clear'
  query: string
  timestamp: number
}

export interface NavigationEvent {
  type: 'navigate' | 'back' | 'forward'
  from: string
  to: string
  timestamp: number
}

// 工具类型
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P]
}

export type Mutable<T> = {
  -readonly [P in keyof T]: T[P]
}

export type NonNullable<T> = T extends null | undefined ? never : T

export type Awaited<T> = T extends Promise<infer U> ? U : T

// 常量类型
export const THEME_TYPES = ['light', 'dark', 'auto'] as const
export const LOADING_STATES = ['idle', 'loading', 'success', 'error'] as const
export const NETWORK_STATUSES = ['online', 'offline', 'slow'] as const
export const AUTH_STATUSES = ['authenticated', 'unauthenticated', 'pending', 'expired'] as const

// 类型守卫
export function isWeiboPost(obj: any): boolean {
  return obj && typeof obj.id !== 'undefined' && typeof obj.text === 'string'
}

export function isWeiboUser(obj: any): boolean {
  return obj && typeof obj.id !== 'undefined' && typeof obj.screen_name === 'string'
}

export function isApiError(obj: any): boolean {
  return obj && typeof obj.code !== 'undefined' && typeof obj.message === 'string'
}

export function isAuthUser(obj: any): boolean {
  return isWeiboUser(obj) && typeof obj.email !== 'undefined'
}
