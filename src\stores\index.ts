import { createPinia } from 'pinia'
import type { App } from 'vue'

// 声明 Vite 环境变量类型
declare global {
  interface ImportMeta {
    env: {
      DEV?: boolean
      [key: string]: any
    }
  }
}

// 创建 Pinia 实例
export const pinia = createPinia()

// 可选：添加 Pinia 插件
if (import.meta.env?.DEV) {
  // 开发环境下启用 devtools
  pinia.use(({ store }) => {
    store.$subscribe((mutation) => {
      console.log(`[${store.$id}] ${mutation.type}:`, mutation)
    })
  })
}

// 安装 Pinia 插件的函数
export function setupStore(app: App): void {
  app.use(pinia)
}

// 导出所有 stores
export { useAuthStore } from './auth'
export { useAppStore } from './app'
export { useWeiboStore } from './weibo'

export default pinia
