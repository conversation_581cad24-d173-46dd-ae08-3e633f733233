import httpClient from '../config/http'
import { API_CONFIG } from '../config/api'
import type {
  WeiboUser,
  WeiboPost,
  WeiboSearchParams,
  WeiboListResponse,
  ApiResponse
} from '../types'

interface GetTweetsParams {
  user?: string
  limit?: number
  cursor?: string
  keyword?: string
}

// 实际 API 响应格式
interface ActualTweetsResponse {
  success: boolean
  data: WeiboPost[]
  next_cursor?: string
  message?: string
}

class WeiboService {
  /**
   * 获取微博推文
   * @param params - 查询参数
   * @returns 返回微博数据
   */
  async getTweets(params: GetTweetsParams): Promise<ActualTweetsResponse> {
    try {
      const { data } = await httpClient.get<ApiResponse<ActualTweetsResponse>>(
        API_CONFIG.ENDPOINTS.TWEETS,
        { params }
      )
      return data.data
    } catch (error) {
      console.error('获取微博数据失败:', error)
      throw error
    }
  }

  /**
   * 获取用户信息
   * @param userId - 用户ID
   * @returns 返回用户信息
   */
  async getUserInfo(userId: string): Promise<WeiboUser> {
    try {
      const { data } = await httpClient.get<ApiResponse<WeiboUser>>(
        `${API_CONFIG.ENDPOINTS.USER}/${userId}`
      )
      return data.data
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }

  /**
   * 搜索微博
   * @param params - 搜索参数
   * @returns 返回搜索结果
   */
  async searchTweets(params: WeiboSearchParams): Promise<WeiboListResponse> {
    try {
      const { data } = await httpClient.get<ApiResponse<WeiboListResponse>>(
        API_CONFIG.ENDPOINTS.SEARCH,
        { params }
      )
      return data.data
    } catch (error) {
      console.error('搜索微博失败:', error)
      throw error
    }
  }
}

export const weiboService = new WeiboService()
