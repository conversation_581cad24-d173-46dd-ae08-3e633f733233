/**
 * Pinia Store 功能测试工具
 */
import { useAuthStore, useAppStore, useWeiboStore } from '../stores/index.ts'

interface StoreTestResult {
  success: boolean
  authStore?: ReturnType<typeof useAuthStore>
  appStore?: ReturnType<typeof useAppStore>
  weiboStore?: ReturnType<typeof useWeiboStore>
  error?: string
}

// 声明全局类型
declare global {
  interface Window {
    testStores?: () => StoreTestResult
  }
}

export const testStores = (): StoreTestResult => {
  console.group('🧪 Pinia Store Tests')
  
  try {
    // 测试 Auth Store
    console.log('📝 Testing Auth Store...')
    const authStore = useAuthStore()
    
    console.log('Auth Store State:', {
      isAuthenticated: authStore.isAuthenticated,
      isLoggedIn: authStore.isLoggedIn,
      userName: authStore.userName,
      hasError: authStore.hasError,
      loading: authStore.loading
    })
    
    // 测试 App Store
    console.log('📝 Testing App Store...')
    const appStore = useAppStore()
    
    console.log('App Store State:', {
      loading: appStore.loading,
      theme: appStore.theme,
      isOnline: appStore.isOnline,
      currentPage: appStore.currentPage,
      searchHistory: appStore.searchHistory.length,
      canAutoRefresh: appStore.canAutoRefresh
    })
    
    // 测试 App Store 方法
    appStore.setCurrentPage('test', 'Store Test Page')
    appStore.addSearchHistory('test search')
    console.log('✅ App Store methods work correctly')
    
    // 测试 Weibo Store
    console.log('📝 Testing Weibo Store...')
    const weiboStore = useWeiboStore()
    
    console.log('Weibo Store State:', {
      tweetsCount: weiboStore.tweetsCount,
      hasMore: weiboStore.hasMoreTweets,
      isLoading: weiboStore.isLoading,
      isSearching: weiboStore.isSearching,
      currentUserId: weiboStore.currentUserId
    })
    
    console.log('✅ All stores initialized successfully!')
    
    return {
      success: true,
      authStore,
      appStore,
      weiboStore
    }
    
  } catch (error) {
    console.error('❌ Store test failed:', error)
    return {
      success: false,
      error: (error as Error).message
    }
  } finally {
    console.groupEnd()
  }
}

// 在开发环境中提供全局访问
if (import.meta.env?.DEV && typeof window !== 'undefined') {
  window.testStores = testStores
  console.log('🎯 Use window.testStores() to test Pinia stores')
}
