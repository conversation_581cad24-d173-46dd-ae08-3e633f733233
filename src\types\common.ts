/**
 * 通用类型定义
 */

// 基础响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message?: string
  code?: number
  timestamp?: string
}

// 分页相关类型
export interface PaginationParams {
  page?: number
  pageSize?: number
  since_id?: string
  max_id?: string
}

export interface PaginationInfo {
  page: number
  pageSize: number
  total: number
  hasMore: boolean
  since_id?: string
  max_id?: string
}

// 加载状态类型
export type LoadingState = 'idle' | 'loading' | 'success' | 'error'

// 错误类型
export interface AppError {
  code: string | number
  message: string
  details?: any
  timestamp?: string
}

// 主题类型
export type ThemeType = 'light' | 'dark' | 'auto'

// 网络状态类型
export type NetworkStatus = 'online' | 'offline' | 'slow'

// 缓存项类型
export interface CacheItem<T = any> {
  data: T
  timestamp: number
  expiry: number
}

// 搜索历史项类型
export interface SearchHistoryItem {
  keyword: string
  timestamp: number
  count: number
}

// 用户偏好设置类型
export interface UserPreferences {
  theme: ThemeType
  autoRefresh: boolean
  refreshInterval: number
  imageQuality: 'low' | 'medium' | 'high'
  showSensitiveContent: boolean
  language: 'zh-CN' | 'en-US'
}

// 应用配置类型
export interface AppConfig {
  apiBaseUrl: string
  environment: 'development' | 'test' | 'production'
  version: string
  features: {
    virtualScroll: boolean
    imagePreview: boolean
    darkMode: boolean
  }
}

// 事件类型
export interface AppEvent {
  type: string
  payload?: any
  timestamp: number
}

// 路由元信息类型
export interface RouteMeta {
  title?: string
  requiresAuth?: boolean
  keepAlive?: boolean
  showInMenu?: boolean
}

// 组件 Props 基础类型
export interface BaseComponentProps {
  class?: string
  style?: string | Record<string, any>
}

// 表单验证规则类型
export interface ValidationRule {
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (value: any) => boolean | string
  message?: string
}

// 文件上传类型
export interface FileUploadOptions {
  accept?: string
  maxSize?: number
  multiple?: boolean
  compress?: boolean
}

// 图片处理选项
export interface ImageProcessOptions {
  width?: number
  height?: number
  quality?: number
  format?: 'jpeg' | 'png' | 'webp'
}

// 通用工具类型
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

// 函数类型
export type AsyncFunction<T = any> = (...args: any[]) => Promise<T>
export type EventHandler<T = any> = (event: T) => void
export type Callback<T = any> = (data: T) => void
