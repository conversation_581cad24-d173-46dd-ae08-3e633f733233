import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import App from './App.vue'
import router from './router'
import { pinia, useAppStore, useWeiboStore } from './stores'
import { useAuth } from './stores/auth'
import './styles/index.scss'

// 开发环境调试工具
if (import.meta.env.DEV) {
  import('./utils/apiTest')
  import('./utils/authDebug')
  import('./utils/scrollDebug')
  import('./utils/storeTest')
  import('./utils/validateStores')
  import('./utils/finalTest')
}

const app = createApp(App)

// 使用 Pinia 状态管理
app.use(pinia)

// Element Plus 全局配置
app.use(ElementPlus, {
  // 确保图片预览组件正确初始化
  zIndex: 3000
})

// 使用路由
app.use(router)

app.mount('#app')

// 在应用挂载后初始化 stores
const appStore = useAppStore()
const weiboStore = useWeiboStore()
const { initAuth } = useAuth()

// 初始化应用
appStore.initApp()
weiboStore.init()
initAuth()