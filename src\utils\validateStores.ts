/**
 * 验证 Pinia Store 迁移是否成功
 */
import { useAuthStore, useAppStore, useWeiboStore } from '../stores/index.ts'

interface ValidationResults {
  authStore: boolean
  appStore: boolean
  weiboStore: boolean
  overall: boolean
  error?: string
}

// 声明全局类型
declare global {
  interface Window {
    validateStoresMigration?: () => ValidationResults
  }
}

export const validateStoresMigration = (): ValidationResults => {
  console.group('🔍 Validating Pinia Store Migration')
  
  const results: ValidationResults = {
    authStore: false,
    appStore: false,
    weiboStore: false,
    overall: false
  }
  
  try {
    // 验证 Auth Store
    console.log('📝 Validating Auth Store...')
    const authStore = useAuthStore()
    
    // 检查必要的状态属性
    const authStateValid = typeof authStore.isAuthenticated === 'boolean' &&
                          typeof authStore.loading === 'boolean' &&
                          authStore.hasOwnProperty('userInfo') &&
                          authStore.hasOwnProperty('authToken')
    
    // 检查必要的 getters
    const authGettersValid = typeof authStore.isLoggedIn === 'boolean' &&
                            typeof authStore.hasError === 'boolean' &&
                            typeof authStore.tokenPreview === 'string'
    
    // 检查必要的方法
    const authMethodsValid = typeof authStore.initAuth === 'function' &&
                            typeof authStore.setAuth === 'function' &&
                            typeof authStore.logout === 'function' &&
                            typeof authStore.getAuthHeaders === 'function'
    
    results.authStore = authStateValid && authGettersValid && authMethodsValid
    console.log(`Auth Store: ${results.authStore ? '✅' : '❌'}`, {
      state: authStateValid,
      getters: authGettersValid,
      methods: authMethodsValid
    })
    
    // 验证 App Store
    console.log('📝 Validating App Store...')
    const appStore = useAppStore()
    
    // 检查必要的状态属性
    const appStateValid = typeof appStore.loading === 'boolean' &&
                         typeof appStore.theme === 'string' &&
                         typeof appStore.isOnline === 'boolean' &&
                         Array.isArray(appStore.searchHistory)
    
    // 检查必要的 getters
    const appGettersValid = typeof appStore.hasError === 'boolean' &&
                           typeof appStore.isDarkTheme === 'boolean' &&
                           typeof appStore.canAutoRefresh === 'boolean'
    
    // 检查必要的方法
    const appMethodsValid = typeof appStore.setLoading === 'function' &&
                           typeof appStore.toggleTheme === 'function' &&
                           typeof appStore.addSearchHistory === 'function' &&
                           typeof appStore.initApp === 'function'
    
    results.appStore = appStateValid && appGettersValid && appMethodsValid
    console.log(`App Store: ${results.appStore ? '✅' : '❌'}`, {
      state: appStateValid,
      getters: appGettersValid,
      methods: appMethodsValid
    })
    
    // 验证 Weibo Store
    console.log('📝 Validating Weibo Store...')
    const weiboStore = useWeiboStore()
    
    // 检查必要的状态属性
    const weiboStateValid = Array.isArray(weiboStore.tweets) &&
                           typeof weiboStore.loading === 'boolean' &&
                           typeof weiboStore.hasMore === 'boolean' &&
                           weiboStore.cache instanceof Map
    
    // 检查必要的 getters
    const weiboGettersValid = typeof weiboStore.tweetsCount === 'number' &&
                             typeof weiboStore.isLoading === 'boolean' &&
                             typeof weiboStore.hasMoreTweets === 'boolean'
    
    // 检查必要的方法
    const weiboMethodsValid = typeof weiboStore.loadTweets === 'function' &&
                             typeof weiboStore.setLoading === 'function' &&
                             typeof weiboStore.resetState === 'function' &&
                             typeof weiboStore.init === 'function'
    
    results.weiboStore = weiboStateValid && weiboGettersValid && weiboMethodsValid
    console.log(`Weibo Store: ${results.weiboStore ? '✅' : '❌'}`, {
      state: weiboStateValid,
      getters: weiboGettersValid,
      methods: weiboMethodsValid
    })
    
    // 整体验证
    results.overall = results.authStore && results.appStore && results.weiboStore
    
    console.log(`\n🎯 Migration Validation: ${results.overall ? '✅ SUCCESS' : '❌ FAILED'}`)
    
    if (results.overall) {
      console.log('🎉 All Pinia stores are working correctly!')
      console.log('📊 Migration Summary:')
      console.log('  - Auth Store: Migrated from composable to Pinia store')
      console.log('  - App Store: New global application state management')
      console.log('  - Weibo Store: New data management with caching')
      console.log('  - Backward compatibility: useAuth() function preserved')
    } else {
      console.warn('⚠️ Some stores have issues. Check the details above.')
    }
    
    return results
    
  } catch (error) {
    console.error('❌ Validation failed:', error)
    return { ...results, error: (error as Error).message }
  } finally {
    console.groupEnd()
  }
}

// 在开发环境中自动运行验证
if (import.meta.env?.DEV) {
  // 延迟执行，确保所有 stores 都已初始化
  setTimeout(() => {
    validateStoresMigration()
  }, 1000)
  
  // 提供全局访问
  if (typeof window !== 'undefined') {
    window.validateStoresMigration = validateStoresMigration
    console.log('🎯 Use window.validateStoresMigration() to re-run validation')
  }
}
