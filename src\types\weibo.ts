/**
 * 微博相关类型定义
 */

import type { PaginationInfo } from './common'

// 微博用户类型
export interface WeiboUser {
  id: string | number
  screen_name: string
  name: string
  profile_image_url: string
  profile_url?: string
  description?: string
  followers_count?: number
  friends_count?: number
  statuses_count?: number
  verified?: boolean
  verified_type?: number
  verified_reason?: string
  location?: string
  gender?: 'm' | 'f' | 'n'
  created_at?: string
}

// 微博图片类型
export interface WeiboImage {
  thumbnail_pic?: string
  bmiddle_pic?: string
  original_pic?: string
  pic_id?: string
  pic_status?: number
  width?: number
  height?: number
  type?: string
  url?: string
}

// 微博视频类型
export interface WeiboVideo {
  object_id?: string
  media_id?: string
  video_url?: string
  thumbnail_url?: string
  duration?: number
  width?: number
  height?: number
  size?: number
}

// 微博话题类型
export interface WeiboTopic {
  topic_title: string
  topic_url?: string
}

// 微博地理位置类型
export interface WeiboGeo {
  longitude?: number
  latitude?: number
  city?: string
  province?: string
  city_name?: string
  province_name?: string
}

// 微博转发信息类型
export interface WeiboRetweetInfo {
  reposts_count: number
  comments_count: number
  attitudes_count: number
}

// 微博内容类型
export interface WeiboPost {
  id: string | number
  idstr?: string
  text: string
  text_raw?: string
  source?: string
  created_at: string
  user: WeiboUser
  
  // 互动数据
  reposts_count: number
  comments_count: number
  attitudes_count: number
  
  // 媒体内容
  pic_urls?: WeiboImage[]
  pic_num?: number
  original_pic?: string
  thumbnail_pic?: string
  bmiddle_pic?: string
  pic_infos?: Record<string, any>
  pics?: WeiboImage[]
  
  // 视频内容
  page_info?: {
    type?: string
    object_id?: string
    media_info?: WeiboVideo
  }
  
  // 转发内容
  retweeted_status?: WeiboPost
  
  // 地理位置
  geo?: WeiboGeo
  region_name?: string

  // 话题
  topic_struct?: WeiboTopic[]
  
  // 其他属性
  favorited?: boolean
  truncated?: boolean
  in_reply_to_status_id?: string
  in_reply_to_user_id?: string
  in_reply_to_screen_name?: string
  mid?: string
  annotations?: any[]
  
  // 可见性
  visible?: {
    type: number
    list_id?: number
  }
  
  // 标签
  tags?: string[]
  
  // 是否为广告
  is_ad?: boolean
  
  // 微博类型
  mblogtype?: number
}

// 微博列表响应类型
export interface WeiboListResponse {
  statuses: WeiboPost[]
  total_number?: number
  hasvisible?: boolean
  previous_cursor?: string
  next_cursor?: string
  since_id?: string
  max_id?: string
  pagination?: PaginationInfo
}

// 微博搜索参数类型
export interface WeiboSearchParams {
  q: string // 搜索关键词
  uid?: string | number // 用户ID
  page?: number
  count?: number
  since_id?: string
  max_id?: string
  type?: 'all' | 'ori' | 'pic' | 'video' // 搜索类型
  sort?: 'time' | 'hot' // 排序方式
  region?: string // 地区
  starttime?: string // 开始时间
  endtime?: string // 结束时间
  hasori?: 0 | 1 // 是否包含原创
  haspic?: 0 | 1 // 是否包含图片
  hasvideo?: 0 | 1 // 是否包含视频
  hasmusic?: 0 | 1 // 是否包含音乐
  haslink?: 0 | 1 // 是否包含链接
}

// 微博详情参数类型
export interface WeiboDetailParams {
  id: string | number
  isBase62?: boolean
}

// 用户微博参数类型
export interface UserWeiboParams {
  uid: string | number
  screen_name?: string
  page?: number
  count?: number
  since_id?: string
  max_id?: string
  trim_user?: 0 | 1
  feature?: 0 | 1 | 2 // 0-全部，1-原创，2-图片
}

// 微博统计信息类型
export interface WeiboStats {
  total: number
  today: number
  week: number
  month: number
}

// 微博缓存键类型
export type WeiboCacheKey = 
  | `user_${string | number}`
  | `search_${string}`
  | `detail_${string | number}`
  | `timeline_${string | number}`

// 微博操作类型
export type WeiboAction = 
  | 'like'
  | 'unlike' 
  | 'repost'
  | 'comment'
  | 'favorite'
  | 'unfavorite'
  | 'follow'
  | 'unfollow'

// 微博过滤选项类型
export interface WeiboFilterOptions {
  hideRetweets?: boolean
  hideAds?: boolean
  hideTopics?: string[]
  hideUsers?: string[]
  minInteractions?: number
  maxAge?: number // 天数
  contentType?: 'all' | 'original' | 'retweet'
  mediaType?: 'all' | 'text' | 'image' | 'video'
}
