<template>
  <div class="store-test">
    <el-card class="test-card">
      <template #header>
        <h2>🧪 Pinia Store 功能测试</h2>
      </template>
      
      <!-- Auth Store 测试 -->
      <el-card class="mb-4">
        <template #header>
          <h3>🔐 Auth Store</h3>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="认证状态">
            <el-tag :type="authStore.isAuthenticated ? 'success' : 'danger'">
              {{ authStore.isAuthenticated ? '已认证' : '未认证' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="登录状态">
            <el-tag :type="authStore.isLoggedIn ? 'success' : 'info'">
              {{ authStore.isLoggedIn ? '已登录' : '未登录' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="用户名">
            {{ authStore.userName || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="加载状态">
            <el-tag :type="authStore.loading ? 'warning' : 'success'">
              {{ authStore.loading ? '加载中' : '空闲' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="错误状态">
            <el-tag :type="authStore.hasError ? 'danger' : 'success'">
              {{ authStore.hasError ? authStore.error : '无错误' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="Token 预览">
            {{ authStore.tokenPreview || '无 Token' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="mt-4">
          <el-button @click="testAuthMethods" type="primary">测试 Auth 方法</el-button>
          <el-button @click="authStore.refreshAuth()" type="info">刷新认证状态</el-button>
        </div>
      </el-card>

      <!-- App Store 测试 -->
      <el-card class="mb-4">
        <template #header>
          <h3>📱 App Store</h3>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="主题">
            <el-tag :type="appStore.isDarkTheme ? 'info' : 'warning'">
              {{ appStore.theme }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="网络状态">
            <el-tag :type="appStore.isOnline ? 'success' : 'danger'">
              {{ appStore.isOnline ? '在线' : '离线' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="当前页面">
            {{ appStore.currentPage || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="页面标题">
            {{ appStore.pageTitle || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="搜索历史">
            {{ appStore.searchHistory.length }} 条记录
          </el-descriptions-item>
          <el-descriptions-item label="自动刷新">
            <el-tag :type="appStore.canAutoRefresh ? 'success' : 'info'">
              {{ appStore.canAutoRefresh ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="mt-4">
          <el-button @click="appStore.toggleTheme()" type="primary">切换主题</el-button>
          <el-button @click="testAppMethods" type="info">测试 App 方法</el-button>
          <el-button @click="appStore.clearSearchHistory()" type="warning">清除搜索历史</el-button>
        </div>
      </el-card>

      <!-- Weibo Store 测试 -->
      <el-card class="mb-4">
        <template #header>
          <h3>📝 Weibo Store</h3>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="微博数量">
            {{ weiboStore.tweetsCount }}
          </el-descriptions-item>
          <el-descriptions-item label="加载状态">
            <el-tag :type="weiboStore.isLoading ? 'warning' : 'success'">
              {{ weiboStore.isLoading ? '加载中' : '空闲' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="加载更多状态">
            <el-tag :type="weiboStore.isLoadingMore ? 'warning' : 'success'">
              {{ weiboStore.isLoadingMore ? '加载中' : '空闲' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="是否有更多">
            <el-tag :type="weiboStore.hasMoreTweets ? 'success' : 'info'">
              {{ weiboStore.hasMoreTweets ? '有更多' : '无更多' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="搜索状态">
            <el-tag :type="weiboStore.isSearching ? 'warning' : 'info'">
              {{ weiboStore.isSearching ? '搜索中' : '未搜索' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="当前用户ID">
            {{ weiboStore.currentUserId || '未设置' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="mt-4">
          <el-button @click="testWeiboMethods" type="primary">测试 Weibo 方法</el-button>
          <el-button @click="weiboStore.resetState()" type="warning">重置状态</el-button>
          <el-button @click="weiboStore.clearCache()" type="info">清除缓存</el-button>
        </div>
      </el-card>

      <!-- 测试结果 -->
      <el-card v-if="testResults.length > 0">
        <template #header>
          <h3>📊 测试结果</h3>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="(result, index) in testResults"
            :key="index"
            :type="result.success ? 'success' : 'danger'"
            :timestamp="result.timestamp"
          >
            {{ result.message }}
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useAuthStore, useAppStore, useWeiboStore } from '../stores/index.ts'

// 获取 stores
const authStore = useAuthStore()
const appStore = useAppStore()
const weiboStore = useWeiboStore()

// 测试结果
const testResults = ref([])

// 添加测试结果
const addTestResult = (message, success = true) => {
  testResults.value.unshift({
    message,
    success,
    timestamp: new Date().toLocaleTimeString()
  })
}

// 测试 Auth Store 方法
const testAuthMethods = () => {
  try {
    // 测试设置认证状态
    const testToken = 'test_token_' + Date.now()
    const testUser = { screen_name: 'Test User', id: '123' }
    
    const result = authStore.setAuth(testToken, testUser)
    if (result) {
      addTestResult('✅ Auth Store setAuth 方法测试成功')
    } else {
      addTestResult('❌ Auth Store setAuth 方法测试失败', false)
    }
    
    // 测试获取认证头部
    const headers = authStore.getAuthHeaders()
    if (headers && headers.Authorization) {
      addTestResult('✅ Auth Store getAuthHeaders 方法测试成功')
    } else {
      addTestResult('❌ Auth Store getAuthHeaders 方法测试失败', false)
    }
    
  } catch (error) {
    addTestResult(`❌ Auth Store 测试出错: ${error.message}`, false)
  }
}

// 测试 App Store 方法
const testAppMethods = () => {
  try {
    // 测试设置页面
    appStore.setCurrentPage('test-page', 'Test Page Title')
    addTestResult('✅ App Store setCurrentPage 方法测试成功')
    
    // 测试添加搜索历史
    appStore.addSearchHistory('测试搜索关键词')
    addTestResult('✅ App Store addSearchHistory 方法测试成功')
    
    // 测试更新偏好设置
    appStore.updatePreferences({ autoRefresh: false })
    addTestResult('✅ App Store updatePreferences 方法测试成功')
    
  } catch (error) {
    addTestResult(`❌ App Store 测试出错: ${error.message}`, false)
  }
}

// 测试 Weibo Store 方法
const testWeiboMethods = () => {
  try {
    // 测试设置加载状态
    weiboStore.setLoading(true)
    setTimeout(() => {
      weiboStore.setLoading(false)
      addTestResult('✅ Weibo Store setLoading 方法测试成功')
    }, 1000)
    
    // 测试设置用户资料
    weiboStore.setUserProfile('123', { name: 'Test User' })
    addTestResult('✅ Weibo Store setUserProfile 方法测试成功')
    
    // 测试缓存功能
    weiboStore.setCachedData('test-key', { data: 'test' })
    const cached = weiboStore.getCachedData('test-key')
    if (cached) {
      addTestResult('✅ Weibo Store 缓存功能测试成功')
    } else {
      addTestResult('❌ Weibo Store 缓存功能测试失败', false)
    }
    
  } catch (error) {
    addTestResult(`❌ Weibo Store 测试出错: ${error.message}`, false)
  }
}

// 页面加载时运行基础测试
addTestResult('🚀 Pinia Store 测试页面已加载')
</script>

<style scoped>
.store-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-card {
  margin-bottom: 20px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}
</style>
