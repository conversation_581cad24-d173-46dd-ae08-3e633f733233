import { defineStore } from 'pinia'
import { weiboService } from '../services/weiboService.ts'

export const useWeiboStore = defineStore('weibo', {
  state: () => ({
    // 微博数据
    tweets: [],
    currentUser: null,
    
    // 分页状态
    nextCursor: null,
    hasMore: true,
    
    // 加载状态
    loading: false,
    loadingMore: false,
    error: null,
    
    // 搜索状态
    currentKeyword: '',
    searchResults: [],
    
    // 缓存
    cache: new Map(),
    cacheExpiry: 5 * 60 * 1000, // 5分钟缓存
    
    // 用户状态
    currentUserId: null,
    userProfiles: new Map()
  }),

  getters: {
    hasError: (state) => !!state.error,
    isLoading: (state) => state.loading,
    isLoadingMore: (state) => state.loadingMore,
    tweetsCount: (state) => state.tweets.length,
    hasMoreTweets: (state) => state.hasMore && !!state.nextCursor,
    isSearching: (state) => !!state.currentKeyword,
    
    // 获取当前用户信息
    currentUserProfile: (state) => {
      if (state.currentUserId) {
        return state.userProfiles.get(state.currentUserId)
      }
      return null
    },
    
    // 获取可见的推文
    visibleTweets: (state) => {
      return state.tweets.filter(tweet => tweet && tweet.id)
    }
  },

  actions: {
    // 设置加载状态
    setLoading(loading) {
      this.loading = loading
      if (!loading) {
        this.error = null
      }
    },

    // 设置加载更多状态
    setLoadingMore(loadingMore) {
      this.loadingMore = loadingMore
    },

    // 设置错误
    setError(error) {
      this.error = error
      this.loading = false
      this.loadingMore = false
      if (error) {
        console.error('Weibo Store Error:', error)
      }
    },

    // 清除错误
    clearError() {
      this.error = null
    },

    // 生成缓存键
    getCacheKey(userId, keyword = '', cursor = '') {
      return `${userId}-${keyword}-${cursor}`
    },

    // 检查缓存是否有效
    isCacheValid(timestamp) {
      return Date.now() - timestamp < this.cacheExpiry
    },

    // 获取缓存数据
    getCachedData(key) {
      const cached = this.cache.get(key)
      if (cached && this.isCacheValid(cached.timestamp)) {
        return cached.data
      }
      return null
    },

    // 设置缓存数据
    setCachedData(key, data) {
      this.cache.set(key, {
        data,
        timestamp: Date.now()
      })
    },

    // 清除过期缓存
    clearExpiredCache() {
      for (const [key, value] of this.cache.entries()) {
        if (!this.isCacheValid(value.timestamp)) {
          this.cache.delete(key)
        }
      }
    },

    // 加载微博数据
    async loadTweets(userId, keyword = '', loadMore = false) {
      try {
        if (!loadMore) {
          this.setLoading(true)
          this.tweets = []
          this.nextCursor = null
          this.hasMore = true
        } else {
          this.setLoadingMore(true)
        }

        this.currentUserId = userId
        this.currentKeyword = keyword

        // 检查缓存
        const cacheKey = this.getCacheKey(userId, keyword, loadMore ? this.nextCursor : '')
        const cachedData = this.getCachedData(cacheKey)
        
        if (cachedData && !loadMore) {
          this.tweets = cachedData.tweets || []
          this.nextCursor = cachedData.next_cursor
          this.hasMore = !!cachedData.next_cursor
          this.setLoading(false)
          return { success: true, fromCache: true }
        }

        // 请求参数
        const params = {
          user: userId,
          limit: 5,
          cursor: loadMore ? this.nextCursor : null,
          keyword: keyword
        }

        const response = await weiboService.getTweets(params)

        if (response?.success) {
          const newTweets = response.data?.length ? response.data.map(tweet => ({
            ...tweet,
            isCardVisible: false
          })) : []

          if (loadMore) {
            this.tweets = [...this.tweets, ...newTweets]
          } else {
            this.tweets = newTweets
          }

          this.nextCursor = response.next_cursor || null
          this.hasMore = !!response.next_cursor

          // 缓存数据
          this.setCachedData(cacheKey, {
            tweets: this.tweets,
            next_cursor: this.nextCursor
          })

          return { success: true, fromCache: false }
        } else {
          throw new Error(response?.message || '加载失败')
        }
      } catch (error) {
        this.setError(error.message || '加载微博数据失败')
        return { success: false, error: error.message }
      } finally {
        this.setLoading(false)
        this.setLoadingMore(false)
      }
    },

    // 刷新数据
    async refreshTweets() {
      if (this.currentUserId) {
        // 清除相关缓存
        const cacheKey = this.getCacheKey(this.currentUserId, this.currentKeyword)
        this.cache.delete(cacheKey)
        
        return await this.loadTweets(this.currentUserId, this.currentKeyword, false)
      }
    },

    // 加载更多
    async loadMoreTweets() {
      if (this.hasMoreTweets && !this.loadingMore && this.currentUserId) {
        return await this.loadTweets(this.currentUserId, this.currentKeyword, true)
      }
    },

    // 搜索微博
    async searchTweets(userId, keyword) {
      return await this.loadTweets(userId, keyword, false)
    },

    // 设置用户资料
    setUserProfile(userId, profile) {
      this.userProfiles.set(userId, profile)
    },

    // 重置状态
    resetState() {
      this.tweets = []
      this.nextCursor = null
      this.hasMore = true
      this.loading = false
      this.loadingMore = false
      this.error = null
      this.currentKeyword = ''
      this.currentUserId = null
    },

    // 清除缓存
    clearCache() {
      this.cache.clear()
    },

    // 初始化
    init() {
      // 定期清理过期缓存
      setInterval(() => {
        this.clearExpiredCache()
      }, 60000) // 每分钟清理一次
    }
  }
})
