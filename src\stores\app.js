import { defineStore } from 'pinia'

export const useAppStore = defineStore('app', {
  state: () => ({
    // 应用全局状态
    loading: false,
    error: null,
    
    // UI 状态
    sidebarCollapsed: false,
    theme: 'light',
    
    // 网络状态
    isOnline: navigator.onLine,
    
    // 页面状态
    currentPage: '',
    pageTitle: '',
    
    // 搜索状态
    globalSearchKeyword: '',
    searchHistory: JSON.parse(localStorage.getItem('search_history') || '[]'),
    
    // 用户偏好设置
    preferences: {
      autoRefresh: true,
      refreshInterval: 30000, // 30秒
      imageQuality: 'high',
      showAnimations: true,
      compactMode: false
    }
  }),

  getters: {
    hasError: (state) => !!state.error,
    isLoading: (state) => state.loading,
    isDarkTheme: (state) => state.theme === 'dark',
    recentSearches: (state) => state.searchHistory.slice(0, 10),
    canAutoRefresh: (state) => state.preferences.autoRefresh && state.isOnline
  },

  actions: {
    // 设置加载状态
    setLoading(loading) {
      this.loading = loading
    },

    // 设置错误信息
    setError(error) {
      this.error = error
      if (error) {
        console.error('App Error:', error)
      }
    },

    // 清除错误
    clearError() {
      this.error = null
    },

    // 切换主题
    toggleTheme() {
      this.theme = this.theme === 'light' ? 'dark' : 'light'
      this.savePreferences()
    },

    // 设置主题
    setTheme(theme) {
      if (['light', 'dark'].includes(theme)) {
        this.theme = theme
        this.savePreferences()
      }
    },

    // 切换侧边栏
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed
    },

    // 设置网络状态
    setOnlineStatus(isOnline) {
      this.isOnline = isOnline
    },

    // 设置当前页面
    setCurrentPage(page, title = '') {
      this.currentPage = page
      this.pageTitle = title
      if (title) {
        document.title = title
      }
    },

    // 添加搜索历史
    addSearchHistory(keyword) {
      if (!keyword || keyword.trim() === '') return
      
      const trimmedKeyword = keyword.trim()
      
      // 移除重复项
      this.searchHistory = this.searchHistory.filter(item => item !== trimmedKeyword)
      
      // 添加到开头
      this.searchHistory.unshift(trimmedKeyword)
      
      // 限制历史记录数量
      if (this.searchHistory.length > 50) {
        this.searchHistory = this.searchHistory.slice(0, 50)
      }
      
      // 保存到 localStorage
      localStorage.setItem('search_history', JSON.stringify(this.searchHistory))
    },

    // 清除搜索历史
    clearSearchHistory() {
      this.searchHistory = []
      localStorage.removeItem('search_history')
    },

    // 设置全局搜索关键词
    setGlobalSearchKeyword(keyword) {
      this.globalSearchKeyword = keyword
    },

    // 更新用户偏好
    updatePreferences(newPreferences) {
      this.preferences = { ...this.preferences, ...newPreferences }
      this.savePreferences()
    },

    // 保存偏好设置到 localStorage
    savePreferences() {
      const settings = {
        theme: this.theme,
        preferences: this.preferences
      }
      localStorage.setItem('app_preferences', JSON.stringify(settings))
    },

    // 从 localStorage 加载偏好设置
    loadPreferences() {
      try {
        const saved = localStorage.getItem('app_preferences')
        if (saved) {
          const settings = JSON.parse(saved)
          if (settings.theme) {
            this.theme = settings.theme
          }
          if (settings.preferences) {
            this.preferences = { ...this.preferences, ...settings.preferences }
          }
        }
      } catch (error) {
        console.warn('Failed to load preferences:', error)
      }
    },

    // 初始化应用状态
    initApp() {
      this.loadPreferences()
      
      // 监听网络状态变化
      window.addEventListener('online', () => this.setOnlineStatus(true))
      window.addEventListener('offline', () => this.setOnlineStatus(false))
      
      // 应用主题
      document.documentElement.setAttribute('data-theme', this.theme)
    },

    // 重置应用状态
    resetApp() {
      this.loading = false
      this.error = null
      this.globalSearchKeyword = ''
      this.currentPage = ''
      this.pageTitle = ''
    }
  }
})
