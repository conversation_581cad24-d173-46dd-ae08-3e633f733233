/**
 * API 相关类型定义
 */

import type {
  ApiResponse,
  PaginationParams,
  AppError
} from './common'
import type { 
  WeiboPost, 
  WeiboSearchParams, 
  WeiboDetailParams,
  UserWeiboParams,
  WeiboListResponse 
} from './weibo'
import type { 
  AuthUser, 
  AuthToken, 
  UserSearchParams,
  UserListResponse,
  FollowListParams 
} from './user'

// HTTP 方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS'

// 请求配置类型
export interface RequestConfig {
  method?: HttpMethod
  url: string
  params?: Record<string, any>
  data?: any
  headers?: Record<string, string>
  timeout?: number
  withCredentials?: boolean
  responseType?: 'json' | 'text' | 'blob' | 'arraybuffer'
  onUploadProgress?: (progressEvent: any) => void
  onDownloadProgress?: (progressEvent: any) => void
}

// 响应类型
export interface ApiResponseData<T = any> {
  data: T
  status: number
  statusText: string
  headers: Record<string, string>
  config: RequestConfig
}

// API 错误类型
export interface ApiError extends AppError {
  status?: number
  statusText?: string
  url?: string
  method?: HttpMethod
  response?: any
}

// API 端点配置类型
export interface ApiEndpoint {
  url: string
  method: HttpMethod
  auth?: boolean
  cache?: boolean
  timeout?: number
}

// API 配置类型
export interface ApiConfig {
  baseURL: string
  timeout: number
  headers: Record<string, string>
  withCredentials: boolean
  endpoints: Record<string, ApiEndpoint>
}

// 微博 API 接口类型
export interface WeiboApiService {
  // 获取用户微博
  getUserWeibos(params: UserWeiboParams): Promise<ApiResponse<WeiboListResponse>>
  
  // 搜索微博
  searchWeibos(params: WeiboSearchParams): Promise<ApiResponse<WeiboListResponse>>
  
  // 获取微博详情
  getWeiboDetail(params: WeiboDetailParams): Promise<ApiResponse<WeiboPost>>
  
  // 获取热门微博
  getHotWeibos(params?: PaginationParams): Promise<ApiResponse<WeiboListResponse>>
  
  // 获取时间线
  getTimeline(params?: PaginationParams): Promise<ApiResponse<WeiboListResponse>>
}

// 用户 API 接口类型
export interface UserApiService {
  // 获取用户信息
  getUserInfo(uid: string | number): Promise<ApiResponse<AuthUser>>
  
  // 搜索用户
  searchUsers(params: UserSearchParams): Promise<ApiResponse<UserListResponse>>
  
  // 获取关注列表
  getFollowing(params: FollowListParams): Promise<ApiResponse<UserListResponse>>
  
  // 获取粉丝列表
  getFollowers(params: FollowListParams): Promise<ApiResponse<UserListResponse>>
  
  // 更新用户资料
  updateProfile(data: any): Promise<ApiResponse<AuthUser>>
}

// 认证 API 接口类型
export interface AuthApiService {
  // 开始认证流程
  startAuth(backUrl?: string): Promise<void>
  
  // 处理认证回调
  handleCallback(params: any): Promise<ApiResponse<{ token: string; user: AuthUser }>>
  
  // 刷新令牌
  refreshToken(refreshToken: string): Promise<ApiResponse<AuthToken>>
  
  // 登出
  logout(): Promise<ApiResponse<void>>
  
  // 验证令牌
  verifyToken(token: string): Promise<ApiResponse<AuthUser>>
}

// 上传 API 接口类型
export interface UploadApiService {
  // 上传图片
  uploadImage(file: File, options?: any): Promise<ApiResponse<{ url: string; id: string }>>
  
  // 上传头像
  uploadAvatar(file: File): Promise<ApiResponse<{ url: string }>>
  
  // 批量上传
  uploadMultiple(files: File[]): Promise<ApiResponse<Array<{ url: string; id: string }>>>
}

// 缓存 API 接口类型
export interface CacheApiService {
  // 获取缓存
  get<T>(key: string): T | null
  
  // 设置缓存
  set<T>(key: string, value: T, expiry?: number): void
  
  // 删除缓存
  delete(key: string): void
  
  // 清空缓存
  clear(): void
  
  // 检查缓存是否存在
  has(key: string): boolean
}

// 请求拦截器类型
export type RequestInterceptor = (config: RequestConfig) => RequestConfig | Promise<RequestConfig>

// 响应拦截器类型
export type ResponseInterceptor = (response: ApiResponseData) => ApiResponseData | Promise<ApiResponseData>

// 错误拦截器类型
export type ErrorInterceptor = (error: ApiError) => Promise<never>

// API 客户端接口类型
export interface ApiClient {
  // 基础请求方法
  request<T = any>(config: RequestConfig): Promise<ApiResponseData<T>>
  get<T = any>(url: string, params?: any, config?: Partial<RequestConfig>): Promise<ApiResponseData<T>>
  post<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponseData<T>>
  put<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponseData<T>>
  delete<T = any>(url: string, config?: Partial<RequestConfig>): Promise<ApiResponseData<T>>
  
  // 拦截器管理
  addRequestInterceptor(interceptor: RequestInterceptor): number
  addResponseInterceptor(interceptor: ResponseInterceptor): number
  addErrorInterceptor(interceptor: ErrorInterceptor): number
  removeInterceptor(id: number): void
  
  // 配置管理
  setConfig(config: Partial<ApiConfig>): void
  getConfig(): ApiConfig
  
  // 认证管理
  setAuthToken(token: string): void
  clearAuthToken(): void
}

// API 状态类型
export interface ApiState {
  loading: boolean
  error: ApiError | null
  lastRequest?: {
    url: string
    method: HttpMethod
    timestamp: number
  }
}

// API 统计信息类型
export interface ApiStats {
  totalRequests: number
  successRequests: number
  errorRequests: number
  averageResponseTime: number
  lastRequestTime?: number
  errorRate: number
}

// 批量请求配置类型
export interface BatchRequestConfig {
  requests: RequestConfig[]
  concurrent?: number
  failFast?: boolean
  retryOnError?: boolean
  maxRetries?: number
}

// 批量请求结果类型
export interface BatchRequestResult<T = any> {
  results: Array<ApiResponseData<T> | ApiError>
  success: boolean
  completed: number
  failed: number
  duration: number
}
