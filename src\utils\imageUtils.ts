import { imageProxy } from './imageProxy.ts'
import type { WeiboPost } from '../types'

interface PicInfo {
  large?: { url: string }
  original?: { url: string }
  url?: string
}

interface PicInfos {
  [key: string]: PicInfo
}

/**
 * 从微博数据中提取图片URL列表
 * @param card - 微博卡片数据
 * @returns 处理后的图片URL数组
 */
export const extractImageUrls = (card: WeiboPost | null | undefined): string[] => {
  if (!card) return []
  
  let images: string[] = []
  
  if (card.pic_infos) {
    images = getImageUrlsFromPicInfos(card.pic_infos)
  } else if (card.pics) {
    images = getImageUrlsFromPics(card.pics)
  }
  
  return images
    .map(url => imageProxy.proxyImage(url))
    .filter(url => url && url.trim() !== '')
}

/**
 * 从 pic_infos 对象中提取图片URL
 * @param picInfos - 图片信息对象
 * @returns 图片URL数组
 */
const getImageUrlsFromPicInfos = (picInfos: PicInfos | null | undefined): string[] => {
  if (!picInfos) return []
  
  return Object.values(picInfos).map(pic => {
    return pic.large?.url || pic.original?.url || ''
  }).filter(Boolean)
}

/**
 * 从 pics 数组中提取图片URL
 * @param pics - 图片数组
 * @returns 图片URL数组
 */
const getImageUrlsFromPics = (pics: PicInfo[] | null | undefined): string[] => {
  if (!pics) return []
  
  return pics.map(pic => {
    return pic.large?.url || pic.original?.url || pic.url || ''
  }).filter(Boolean)
}
