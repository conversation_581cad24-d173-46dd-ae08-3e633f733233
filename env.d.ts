/// <reference types="vite/client" />

// 环境变量类型定义
interface ImportMetaEnv {
  readonly VITE_API_BASE_URL: string
  readonly VITE_APP_ENV: string
  readonly VITE_APP_TITLE: string
  readonly DEV: boolean
  readonly PROD: boolean
  readonly SSR: boolean
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// Vue 组件类型声明
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 全局类型扩展
declare global {
  // 全局常量
  const __APP_ENV__: string
  const __API_BASE_URL__: string

  // Window 对象扩展 (开发环境调试工具)
  interface Window {
    debugAuth?: () => any
    testStores?: () => any
    validateStoresMigration?: () => any
    runFinalTest?: () => any
  }
}
