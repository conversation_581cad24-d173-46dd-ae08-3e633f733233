/**
 * API 连接测试工具
 */
import { getApiUrl, isDevelopment } from '../config/api.js'

export const testApiConnection = async (): Promise<boolean> => {
  if (!isDevelopment()) return false

  console.log('🧪 Testing API connection...')
  
  try {
    const testUrl = getApiUrl('/api/get_subscribe')
    console.log('📡 Test URL:', testUrl)
    
    // 简单的连接测试
    const response = await fetch(testUrl, {
      method: 'HEAD',
      mode: 'cors'
    })
    
    console.log('✅ API connection test result:', {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries())
    })
    
    return true
  } catch (error) {
    console.error('❌ API connection test failed:', error)
    return false
  }
}

// 在开发环境中自动运行测试
if (isDevelopment() && import.meta.env?.DEV) {
  setTimeout(() => {
    testApiConnection()
  }, 1000)
}
