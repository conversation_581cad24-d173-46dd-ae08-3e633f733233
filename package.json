{"name": "weibo-vue-app", "version": "1.0.0", "description": "Modern Vue.js Weibo viewer application", "type": "module", "main": "index.js", "scripts": {"dev": "vite --mode development", "dev:test": "vite --mode test", "build": "vue-tsc && vite build --mode production", "build:test": "vue-tsc && vite build --mode test", "build:dev": "vue-tsc && vite build --mode development", "preview": "vite preview", "serve": "vite preview", "vercel-build": "vue-tsc && vite build --mode production", "type-check": "vue-tsc --noEmit", "type-check:watch": "vue-tsc --noEmit --watch"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.8", "dompurify": "^3.2.3", "element-plus": "^2.8.0", "luxon": "^3.5.0", "pinia": "^3.0.3", "vue": "^3.4.0", "vue-router": "^4.5.1"}, "devDependencies": {"@types/luxon": "^3.7.1", "@types/node": "^24.1.0", "@vitejs/plugin-vue": "^5.0.0", "sass": "^1.89.2", "sass-embedded": "^1.89.2", "typescript": "^5.8.3", "vite": "^5.0.0", "vue-tsc": "^3.0.4"}}