import { defineStore } from 'pinia'

// 主题类型
type ThemeType = 'light' | 'dark'

// 图片质量类型
type ImageQuality = 'low' | 'medium' | 'high'

// 用户偏好设置类型
interface UserPreferences {
  autoRefresh: boolean
  refreshInterval: number
  imageQuality: ImageQuality
  showAnimations: boolean
  compactMode: boolean
}

// App Store 状态类型
interface AppState {
  loading: boolean
  error: string | null
  sidebarCollapsed: boolean
  theme: ThemeType
  isOnline: boolean
  currentPage: string
  pageTitle: string
  globalSearchKeyword: string
  searchHistory: string[]
  preferences: UserPreferences
}

export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    // 应用全局状态
    loading: false,
    error: null,

    // UI 状态
    sidebarCollapsed: false,
    theme: 'light',

    // 网络状态
    isOnline: navigator.onLine,

    // 页面状态
    currentPage: '',
    pageTitle: '',

    // 搜索状态
    globalSearchKeyword: '',
    searchHistory: JSON.parse(localStorage.getItem('search_history') || '[]'),

    // 用户偏好设置
    preferences: {
      autoRefresh: true,
      refreshInterval: 30000, // 30秒
      imageQuality: 'high',
      showAnimations: true,
      compactMode: false
    }
  }),

  getters: {
    hasError: (state): boolean => !!state.error,
    isLoading: (state): boolean => state.loading,
    isDarkTheme: (state): boolean => state.theme === 'dark',
    recentSearches: (state): string[] => state.searchHistory.slice(0, 10),
    canAutoRefresh: (state): boolean => state.preferences.autoRefresh && state.isOnline
  },

  actions: {
    // 设置加载状态
    setLoading(loading: boolean): void {
      this.loading = loading
    },

    // 设置错误信息
    setError(error: string | null): void {
      this.error = error
      if (error) {
        console.error('App Error:', error)
      }
    },

    // 清除错误
    clearError(): void {
      this.error = null
    },

    // 切换主题
    toggleTheme(): void {
      this.theme = this.theme === 'light' ? 'dark' : 'light'
      this.savePreferences()
    },

    // 设置主题
    setTheme(theme: ThemeType): void {
      if (['light', 'dark'].includes(theme)) {
        this.theme = theme
        this.savePreferences()
      }
    },

    // 切换侧边栏
    toggleSidebar(): void {
      this.sidebarCollapsed = !this.sidebarCollapsed
    },

    // 设置网络状态
    setOnlineStatus(isOnline: boolean): void {
      this.isOnline = isOnline
    },

    // 设置当前页面
    setCurrentPage(page: string, title: string = ''): void {
      this.currentPage = page
      this.pageTitle = title
      if (title) {
        document.title = title
      }
    },

    // 添加搜索历史
    addSearchHistory(keyword: string): void {
      if (!keyword || keyword.trim() === '') return

      const trimmedKeyword = keyword.trim()

      // 移除重复项
      this.searchHistory = this.searchHistory.filter(item => item !== trimmedKeyword)

      // 添加到开头
      this.searchHistory.unshift(trimmedKeyword)

      // 限制历史记录数量
      if (this.searchHistory.length > 50) {
        this.searchHistory = this.searchHistory.slice(0, 50)
      }

      // 保存到 localStorage
      localStorage.setItem('search_history', JSON.stringify(this.searchHistory))
    },

    // 清除搜索历史
    clearSearchHistory(): void {
      this.searchHistory = []
      localStorage.removeItem('search_history')
    },

    // 设置全局搜索关键词
    setGlobalSearchKeyword(keyword: string): void {
      this.globalSearchKeyword = keyword
    },

    // 更新用户偏好
    updatePreferences(newPreferences: Partial<UserPreferences>): void {
      this.preferences = { ...this.preferences, ...newPreferences }
      this.savePreferences()
    },

    // 保存偏好设置到 localStorage
    savePreferences(): void {
      const settings = {
        theme: this.theme,
        preferences: this.preferences
      }
      localStorage.setItem('app_preferences', JSON.stringify(settings))
    },

    // 从 localStorage 加载偏好设置
    loadPreferences(): void {
      try {
        const saved = localStorage.getItem('app_preferences')
        if (saved) {
          const settings = JSON.parse(saved)
          if (settings.theme) {
            this.theme = settings.theme
          }
          if (settings.preferences) {
            this.preferences = { ...this.preferences, ...settings.preferences }
          }
        }
      } catch (error) {
        console.warn('Failed to load preferences:', error)
      }
    },

    // 初始化应用状态
    initApp(): void {
      this.loadPreferences()

      // 监听网络状态变化
      window.addEventListener('online', () => this.setOnlineStatus(true))
      window.addEventListener('offline', () => this.setOnlineStatus(false))

      // 应用主题
      document.documentElement.setAttribute('data-theme', this.theme)
    },

    // 重置应用状态
    resetApp(): void {
      this.loading = false
      this.error = null
      this.globalSearchKeyword = ''
      this.currentPage = ''
      this.pageTitle = ''
    }
  }
})
