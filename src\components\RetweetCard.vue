<template>
  <div class="retweet-card" v-if="retweetedStatus">
    <div class="retweet-header">
      <span class="at-user-tag">@{{ userName }}</span>:
    </div>
    <div class="card-content" v-html="processedText"></div>
    
    <ImageGrid 
      :images="images" 
      key-prefix="retweet"
    />
  </div>
</template>

<script>
import { computed } from 'vue'
import { textFormatter } from '../utils/textFormatter.ts'
import { extractImageUrls } from '../utils/imageUtils.ts'
import ImageGrid from './ImageGrid.vue'

export default {
  name: 'RetweetCard',
  components: {
    ImageGrid
  },
  props: {
    retweetedStatus: {
      type: Object,
      default: null
    }
  },
  setup(props) {
    const userName = computed(() => {
      return props.retweetedStatus?.user?.screen_name || '已删除用户'
    })

    const processedText = computed(() => {
      return textFormatter.formatText(props.retweetedStatus?.text)
    })

    const images = computed(() => {
      return extractImageUrls(props.retweetedStatus)
    })

    return {
      userName,
      processedText,
      images
    }
  }
}
</script>

<style lang="scss" scoped>
.retweet-card {
  margin: 0 $space-xl $space-xl;
  padding: $space-lg;
  background: linear-gradient(135deg, $bg-secondary 0%, rgba(248, 250, 252, 0.8) 100%);
  border-radius: $radius-lg;
  border: 1px solid $border-light;
  position: relative;
  transition: all $transition-base;
  
  @include mobile {
    margin: 0 $space-lg $space-lg;
    padding: $space-md;
  }
  
  // 左侧装饰线
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(180deg, $primary-color, $primary-hover);
    border-radius: 0 $radius-sm $radius-sm 0;
  }
  
  &:hover {
    background: $bg-primary;
    box-shadow: $shadow-2;
    transform: translateX(2px);
    border-color: $border-default;
  }
  
  &:active {
    transform: translateX(1px);
  }
}

// 转发头部样式
.retweet-header {
  margin-bottom: $space-md;
  font-weight: 500;
  color: $text-secondary;
  font-size: $font-size-sm;
  display: flex;
  align-items: center;
  gap: $space-xs;
  
  @include mobile {
    font-size: $font-size-xs;
    margin-bottom: $space-sm;
  }
  
  // 转发图标
  &::before {
    content: '↻';
    font-size: $font-size-base;
    color: $primary-color;
    font-weight: bold;
    
    @include mobile {
      font-size: $font-size-sm;
    }
  }
}

// 用户标签样式
:deep(.at-user-tag) {
  display: inline-block;
  color: $primary-color;
  background: $primary-light;
  padding: 2px 6px;
  border-radius: $radius-sm;
  font-weight: 500;
  font-size: $font-size-xs;
  transition: all $transition-fast;
  margin: 0 2px;
  text-decoration: none;
  
  @include mobile {
    padding: 1px 4px;
    font-size: 10px;
  }
  
  &:hover {
    background: $primary-color;
    color: white;
    transform: translateY(-1px);
  }
}

// 转发内容样式
.card-content {
  line-height: $line-height-relaxed;
  color: $text-primary;
  font-size: $font-size-sm;
  word-break: break-word;
  
  @include mobile {
    font-size: $font-size-xs;
    line-height: $line-height-base;
  }
  
  // 内联表情样式
  :deep(.inline-emoji) {
    display: inline-block;
    vertical-align: middle;
    height: 1.1em;
    width: auto;
    margin: 0 1px;
    transition: transform $transition-fast;
    
    &:hover {
      transform: scale(1.15);
    }
  }
  
  // 链接样式
  :deep(a) {
    color: $primary-color;
    text-decoration: none;
    font-weight: 500;
    transition: all $transition-fast;
    border-radius: $radius-sm;
    padding: 1px 3px;
    margin: 0 1px;
    
    &:hover {
      color: $primary-hover;
      background: $primary-light;
      transform: translateY(-1px);
    }
  }
  
  // 话题标签样式
  :deep(.topic-tag) {
    display: inline-block;
    color: $primary-color;
    background: $primary-light;
    padding: 1px 4px;
    border-radius: $radius-sm;
    font-weight: 500;
    font-size: inherit;
    transition: all $transition-fast;
    margin: 0 1px;
    
    &:hover {
      background: $primary-color;
      color: white;
      transform: translateY(-1px);
    }
  }
}

// 转发卡片中的图片网格样式
:deep(.image-grid) {
  padding: 0 0 $space-md;
  margin-top: $space-md;
  
  @include mobile {
    padding: 0 0 $space-sm;
    margin-top: $space-sm;
  }
  
  .el-image {
    border-radius: $radius-sm;
    
    @include mobile {
      border-radius: 4px;
    }
  }
}

// 暗色模式适配
@include dark-mode {
  .retweet-card {
    background: linear-gradient(135deg, $dark-bg-secondary 0%, rgba(58, 59, 60, 0.8) 100%);
    border-color: $dark-border-light;
    
    &:hover {
      background: $dark-bg-primary;
      border-color: $dark-border-default;
    }
  }
  
  .retweet-header {
    color: $dark-text-secondary;
  }
  
  .card-content {
    color: $dark-text-primary;
    
    :deep(a) {
      color: $primary-color;
      
      &:hover {
        background: rgba(22, 119, 255, 0.2);
      }
    }
  }
}

// 高对比度模式
@include high-contrast {
  .retweet-card {
    border: 2px solid currentColor;
    
    &::before {
      background: currentColor;
    }
  }
  
  .card-content :deep(a) {
    text-decoration: underline;
  }
}

// 减少动画模式
@include reduced-motion {
  .retweet-card {
    transition: none !important;
    
    &:hover {
      transform: none !important;
    }
    
    &:active {
      transform: none !important;
    }
  }
  
  :deep(.at-user-tag),
  .card-content :deep(.topic-tag),
  .card-content :deep(a),
  .card-content :deep(.inline-emoji) {
    transition: none !important;
    
    &:hover {
      transform: none !important;
    }
  }
}
</style>