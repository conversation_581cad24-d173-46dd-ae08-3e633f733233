/**
 * 滚动调试工具
 */

let debugPanel: HTMLDivElement | null = null

// 声明全局类型
declare global {
  interface Window {
    scrollDebug?: {
      enable: () => void
      disable: () => void
    }
  }
}

export const enableScrollDebug = (): void => {
  if (debugPanel || !import.meta.env?.DEV) return
  
  // 创建调试面板
  debugPanel = document.createElement('div')
  debugPanel.style.cssText = `
    position: fixed;
    top: 120px;
    left: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px;
    border-radius: 5px;
    font-family: monospace;
    font-size: 12px;
    z-index: 9999;
    min-width: 200px;
  `
  
  document.body.appendChild(debugPanel)
  
  let lastScrollY = 0
  let scrollDirection = 'none'
  
  const updateDebugInfo = (): void => {
    const currentScrollY = window.scrollY
    const scrollDelta = currentScrollY - lastScrollY
    
    if (scrollDelta > 0) {
      scrollDirection = 'down'
    } else if (scrollDelta < 0) {
      scrollDirection = 'up'
    }
    
    const searchBox = document.querySelector('.smart-search-box')
    const isVisible = searchBox ? !searchBox.classList.contains('is-visible') === false : false
    const isFixed = searchBox ? searchBox.classList.contains('is-fixed') : false
    
    if (debugPanel) {
      debugPanel.innerHTML = `
        <div>ScrollY: ${currentScrollY.toFixed(0)}</div>
        <div>Delta: ${scrollDelta.toFixed(0)}</div>
        <div>Direction: ${scrollDirection}</div>
        <div>SearchBox Visible: ${isVisible}</div>
        <div>SearchBox Fixed: ${isFixed}</div>
        <div>Window Height: ${window.innerHeight}</div>
        <div>Document Height: ${document.documentElement.scrollHeight}</div>
      `
    }
    
    lastScrollY = currentScrollY
  }
  
  window.addEventListener('scroll', updateDebugInfo, { passive: true })
  updateDebugInfo()
  
  console.log('📊 Scroll debug enabled. Panel visible in top-left corner.')
}

export const disableScrollDebug = (): void => {
  if (debugPanel) {
    debugPanel.remove()
    debugPanel = null
    console.log('📊 Scroll debug disabled.')
  }
}

// 在开发环境中提供全局访问
if (import.meta.env?.DEV && typeof window !== 'undefined') {
  window.scrollDebug = {
    enable: enableScrollDebug,
    disable: disableScrollDebug
  }
  
  console.log('🎯 Use window.scrollDebug.enable() to start scroll debugging')
}
