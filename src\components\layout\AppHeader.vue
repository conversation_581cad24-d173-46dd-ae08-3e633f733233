<template>
  <header class="app-header">
    <div class="app-header__content">
      <!-- 左侧：Logo、返回按钮和标题 -->
      <div class="app-header__brand">
        <!-- 返回按钮 -->
        <button 
          v-if="showBackButton" 
          class="back-btn" 
          @click="goBack"
          aria-label="返回"
        >
          <el-icon><ArrowLeft /></el-icon>
        </button>
        
        <!-- Logo 和标题 -->
        <router-link to="/" class="brand-link">
          <div class="brand-logo">
            <span class="logo-icon">📱</span>
          </div>
          <h1 class="brand-title">{{ pageTitle }}</h1>
        </router-link>
      </div>
      
      <!-- 右侧：用户状态和操作 -->
      <div class="app-header__actions">
        <!-- 已认证状态 -->
        <div v-if="isAuthenticated" class="user-status">
          <span class="auth-badge">
            <el-icon class="auth-icon"><Check /></el-icon>
            已认证
          </span>
          
          <!-- 调试模式下的清除认证按钮 -->
          <el-button 
            v-if="isDebug" 
            type="danger" 
            size="small" 
            plain
            @click="clearAuth"
          >
            清除认证
          </el-button>
          
          <!-- 退出登录按钮 -->
          <el-button 
            type="primary" 
            size="small" 
            plain
            @click="handleLogout"
          >
            <el-icon><SwitchButton /></el-icon>
            退出登录
          </el-button>
        </div>
        
        <!-- 未认证状态 -->
        <div v-else class="auth-prompt">
          <span class="auth-message">请先进行身份认证</span>
          <el-button 
            type="primary" 
            size="small"
            @click="goToAuth"
          >
            <el-icon><User /></el-icon>
            立即认证
          </el-button>
        </div>
      </div>
    </div>
  </header>
</template>

<script>
import { ref, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '../../stores/auth.ts'
import { ElMessage } from 'element-plus'
import { Check, SwitchButton, User, ArrowLeft } from '@element-plus/icons-vue'
import axios from 'axios'
import { getApiUrl } from '../../config/api.js'

export default {
  name: 'AppHeader',
  components: {
    Check,
    SwitchButton,
    User,
    ArrowLeft
  },
  setup() {
    const router = useRouter()
    const { isAuthenticated, logout, startAuth, setAuth } = useAuth()
    const isDebug = ref(false)
    const showBackButton = ref(false)
    const pageTitle = ref('')
    
    // 监听路由变化，决定是否显示返回按钮
    const updateHeaderState = () => {
      const currentRoute = router.currentRoute.value
      const isHomePage = currentRoute.path === '/'
      
      showBackButton.value = !isHomePage
      
      // 根据路由设置页面标题
      if (currentRoute.name === 'UserProfile') {
        pageTitle.value = `用户 ${currentRoute.params.userId} 的微博`
      } else if (currentRoute.name === 'WeiboDetail') {
        pageTitle.value = '微博详情'
      } else {
        pageTitle.value = '微博浏览器'
      }
    }
    
    // 检查 debug 模式
    const checkDebugMode = () => {
      const urlParams = new URLSearchParams(window.location.search)
      isDebug.value = urlParams.has('debug')
    }
    
    // 检查 JWT 令牌参数
    const checkJwtToken = () => {
      let jwtToken = null
      
      // 方法1: 从 window.location.search 获取
      const urlParams = new URLSearchParams(window.location.search)
      jwtToken = urlParams.get('jwt') || urlParams.get('token')
      
      // 方法2: 从 hash 中的查询参数获取
      if (!jwtToken && window.location.hash) {
        const hashParts = window.location.hash.split('?')
        if (hashParts.length > 1) {
          const hashParams = new URLSearchParams(hashParts[1])
          jwtToken = hashParams.get('jwt') || hashParams.get('token')
        }
      }
      
      // 方法3: 从 Vue Router 的 query 获取
      if (!jwtToken) {
        jwtToken = router.currentRoute.value.query.jwt || router.currentRoute.value.query.token
      }
      
      if (isDebug.value) {
        console.log('🔍 JWT Token Check:', {
          'window.location.href': window.location.href,
          'found_token': jwtToken ? jwtToken.substring(0, 20) + '...' : null
        })
      }
      
      if (jwtToken) {
        // 保存令牌到 localStorage
        localStorage.setItem('auth_token', jwtToken)
        
        // 设置认证状态
        const success = setAuth(jwtToken, null)
        
        if (success) {
          // 清理 URL 参数
          router.replace({ path: router.currentRoute.value.path, query: {} })
          ElMessage.success('认证成功！')
          return true
        } else {
          ElMessage.error('令牌设置失败')
        }
      }
      return false
    }
    
    // 开始认证
    const goToAuth = () => {
      let redirectUrl = getApiUrl(`/api/oauth2?redirect=${encodeURIComponent(window.location.href)}`)
      if (isDebug.value) {
        redirectUrl += '&debug'
      }
      window.location.href = redirectUrl
    }
    
    // 退出登录
    const handleLogout = async () => {
      try {
        // 调用后端注销接口
        await axios.get(getApiUrl('/api/logout'))
        ElMessage.success('已成功注销!')
      } catch (err) {
        console.error('Logout failed:', err)
        ElMessage.warning('注销请求失败，但本地数据已清除')
      }
      
      logout()
      ElMessage.success('已退出登录')
    }
    
    // 返回上一页
    const goBack = () => {
      if (window.history.length > 1) {
        router.go(-1)
      } else {
        router.push('/')
      }
    }
    
    // 清除认证（调试用）
    const clearAuth = () => {
      logout()
      
      // 清除所有 cookies
      const cookies = document.cookie.split(";")
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i]
        const eqPos = cookie.indexOf("=")
        const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie
        document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT"
        document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;domain=" + document.domain + ";path=/"
      }
      
      ElMessage.success('认证数据已清除')
    }
    
    onMounted(() => {
      checkDebugMode()
      updateHeaderState()
      
      // 监听路由变化
      router.afterEach(() => {
        updateHeaderState()
      })
      
      // 延迟检查 JWT 令牌，确保路由完全加载
      nextTick(() => {
        setTimeout(() => {
          checkJwtToken()
        }, 100)
      })
    })
    
    return {
      isAuthenticated,
      isDebug,
      showBackButton,
      pageTitle,
      goToAuth,
      goBack,
      handleLogout,
      clearAuth
    }
  }
}
</script>

<style lang="scss" scoped>
.app-header {
  background: $bg-primary;
  border-bottom: 1px solid $border-light;
  box-shadow: $shadow-1;
  position: sticky;
  top: 0;
  z-index: $z-index-sticky;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  
  &__content {
    max-width: 800px;
    margin: 0 auto;
    padding: $space-lg $space-lg;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 64px;
    
    @media (max-width: #{$breakpoint-mobile - 1px}) {
      padding: $space-md;
    }
  }
  
  &__brand {
    @include flex-start;
    gap: $space-md;
    flex: 1;
    min-width: 0;
    
    .back-btn {
      @include button-base;
      @include button-secondary;
      padding: $space-sm;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      @include flex-center;
      transition: all $transition-base;
      flex-shrink: 0;
      
      &:hover {
        transform: translateX(-2px);
        background: $primary-light;
        color: $primary-color;
      }
      
      .el-icon {
        font-size: 18px;
      }
      
      @include mobile {
        width: 36px;
        height: 36px;
        padding: $space-xs;
        
        .el-icon {
          font-size: 16px;
        }
      }
    }
    
    .brand-link {
      @include flex-start;
      gap: $space-md;
      text-decoration: none;
      color: inherit;
      transition: all $transition-base;
      flex: 1;
      min-width: 0;
      
      &:hover {
        transform: translateY(-1px);
        
        .brand-logo {
          transform: scale(1.1);
        }
      }
    }
    
    .brand-logo {
      @include flex-center;
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, $primary-color, $primary-hover);
      border-radius: $radius-lg;
      transition: all $transition-base;
      
      .logo-icon {
        font-size: 20px;
        filter: brightness(0) invert(1);
      }
    }
    
    .brand-title {
      font-size: $font-size-xl;
      font-weight: 600;
      color: $text-primary;
      margin: 0;
      @include text-ellipsis;
      
      @include mobile {
        font-size: $font-size-base;
      }
    }
  }
  
  &__actions {
    @include flex-start;
    gap: $space-md;
  }
}

// 用户状态样式
.user-status {
  @include flex-start;
  gap: $space-md;
  
  .auth-badge {
    @include flex-start;
    gap: $space-xs;
    padding: $space-xs $space-sm;
    background: linear-gradient(135deg, rgba(103, 194, 58, 0.1), rgba(103, 194, 58, 0.05));
    color: #67c23a;
    border-radius: $radius-md;
    font-size: $font-size-sm;
    font-weight: 500;
    border: 1px solid rgba(103, 194, 58, 0.2);
    
    .auth-icon {
      font-size: 14px;
    }
  }
}

// 认证提示样式
.auth-prompt {
  @include flex-start;
  gap: $space-md;
  
  .auth-message {
    color: $text-secondary;
    font-size: $font-size-sm;
    white-space: nowrap;
    
    @include mobile {
      display: none; // 移动端隐藏提示文字
    }
  }
}

// 响应式适配
@include mobile {
  .app-header {
    &__content {
      padding: $space-md;
      min-height: 56px;
      gap: $space-sm;
    }
    
    &__brand {
      flex: 1;
      min-width: 0;
      
      .brand-link {
        gap: $space-sm;
      }
      
      .brand-logo {
        width: 36px;
        height: 36px;
        flex-shrink: 0;
        
        .logo-icon {
          font-size: 18px;
        }
      }
      
      .brand-title {
        font-size: $font-size-base;
        @include text-ellipsis;
      }
    }
    
    &__actions {
      gap: $space-xs;
      flex-shrink: 0;
    }
  }
  
  .user-status {
    gap: $space-xs;
    
    .auth-badge {
      padding: 4px 6px;
      font-size: 11px;
      
      .auth-icon {
        font-size: 12px;
      }
    }
    
    .el-button {
      padding: 6px 8px;
      font-size: 12px;
      min-height: 28px;
      
      .el-icon {
        font-size: 14px;
      }
    }
  }
  
  .auth-prompt {
    gap: $space-xs;
    
    .auth-message {
      display: none; // 移动端隐藏提示文字
    }
    
    .el-button {
      padding: 6px 12px;
      font-size: 12px;
      min-height: 32px;
      
      .el-icon {
        font-size: 14px;
      }
    }
  }
}

// 暗色模式适配
@include dark-mode {
  .app-header {
    background: $dark-bg-primary;
    border-color: $dark-border-light;
    
    &__brand {
      .brand-title {
        color: $dark-text-primary;
      }
    }
  }
}
</style>