/**
 * API 配置
 */

// 获取环境变量
const getEnvVar = (key, defaultValue = '') => {
  return import.meta.env[key] || defaultValue
}

// API 基础配置
export const API_CONFIG = {
  // 基础 URL
  BASE_URL: getEnvVar('VITE_API_BASE_URL', 'http://localhost:8080'),
  
  // 环境信息
  ENV: getEnvVar('VITE_APP_ENV', 'development'),
  
  // 应用标题
  APP_TITLE: getEnvVar('VITE_APP_TITLE', '微博查看器'),
  
  // 请求超时时间
  TIMEOUT: 10000,
  
  // API 端点
  ENDPOINTS: {
    TWEETS: '/api/tweets',
    USER: '/api/user',
    SEARCH: '/api/search',
    OAUTH_START: '/api/oauth/start',
    OAUTH_CALLBACK: '/api/oauth/callback'
  }
}

// 根据环境获取完整的 API URL
export const getApiUrl = (endpoint) => {
  // 在开发环境中使用代理，避免 CORS 问题
  if (isDevelopment() && import.meta.env.DEV) {
    const path = endpoint.startsWith('/') ? endpoint : `/${endpoint}`
    return path // 使用相对路径，通过 Vite 代理
  }
  
  const baseUrl = API_CONFIG.BASE_URL.replace(/\/$/, '') // 移除末尾斜杠
  const path = endpoint.startsWith('/') ? endpoint : `/${endpoint}`
  return `${baseUrl}${path}`
}

// 环境检查工具
export const isDevelopment = () => API_CONFIG.ENV === 'development'
export const isTest = () => API_CONFIG.ENV === 'test'
export const isProduction = () => API_CONFIG.ENV === 'production'

// 调试信息（仅在开发环境显示）
if (isDevelopment()) {
  console.log('🔧 API Configuration:', {
    baseUrl: API_CONFIG.BASE_URL,
    environment: API_CONFIG.ENV,
    endpoints: API_CONFIG.ENDPOINTS
  })
}